import kai.tensorflow as kai
MODEL_TRANS_ORIGIN='python'
from kai.tensorflow.config.ad_config.ktrain.klearn_utils import data_ops
from kai.tensorflow.config.ad_config.ktrain.AUC import auc as auc_eval
import logging
LOG_FORMAT = "%(asctime)s - %(levelname)s [%(filename)s:%(lineno)s - %(funcName)s] - %(message)s"
logging.basicConfig(level=logging.INFO, format=LOG_FORMAT)
logger = logging.getLogger(__name__)
import sys,os
import time
import math
from datetime import datetime
from datetime import timedelta
import tensorflow as tf
from tensorflow.python.ops import math_ops
#import tensorflow.compat.v1 as tf
import numpy as np
def get_lookup_tbl(K):
    tbl = []
    for i in range(1 << (K)):
        items = []
        for j in range(K):
            if i & (1 << j) != 0:
                items.append(1)
            else:
                items.append(0)
        tbl.append(items)
    return tbl

def get_lookup_tbl_with_neg(K, neg_weight, neg_labels=[1, 8, 9]):
    tbl = []
    for i in range(1 << (K)):
        items = []
        for j in range(K):
            if i & (1 << j) != 0:
                items.append(1.)
            else:
                items.append(0.)
        items.append(1.) # as sample weight
        tbl.append(items)
    for neg in neg_labels:
        tbl[neg][-1] = neg_weight # for neg sample
    # tbl[neg_labels][-1] = neg_weight
    return tbl

def get_sub_matrix(pf):
    sub = [[0.0 for i in range(pf*4)] for j in range(pf*16)]
    for i in range(pf*4):
        sub[4 * i][i] = 1.0
        sub[(4 * i) + 1][i] = 1.0
        sub[(4 * i) + 2][i] = 1.0
        sub[(4 * i) + 3][i] = 1.0
    return sub

def get_cost_sensitive_weight(labels_idx, label_nums):
    weight = [[np.abs(i - j) for i in range(label_nums)] for j in range(label_nums)]
    cs_weight_tbl = tf.constant(weight, dtype=tf.float32, name='cost_sensitive_weight_tbl')
    labels_idx = tf.reshape(tf.cast(labels_idx, tf.int32), [-1])
    return tf.nn.embedding_lookup(cs_weight_tbl, labels_idx)

# 从get_cost_sensitive_weight() 复制, 将小于labels_idx的样本不给额外权重, 而对大于labels_idx的桶样本进行loss累加
def get_seq_sensitive_weight(labels_idx, label_nums):
    weight = [[np.abs(i - j) for i in range(label_nums)] for j in range(label_nums)]
    mask = np.triu(np.ones_like(weight))
    weight *= mask
    cs_weight_tbl = tf.constant(weight, dtype=tf.float32, name='cost_sensitive_weight_tbl')
    labels_idx = tf.reshape(tf.cast(labels_idx, tf.int32), [-1])
    return tf.nn.embedding_lookup(cs_weight_tbl, labels_idx)

def variable_on_cpu(name, shape, initializer=None, trainable=True, is_cpu_ps=True):
    """Helper to create a Variable stored on CPU memory."""
    if is_cpu_ps:
        with tf.device('/cpu:0'):
            return tf.get_variable(name, shape, initializer=initializer, trainable=trainable)
    return tf.get_variable(name, shape, initializer=initializer, trainable=trainable)

# https://docs.corp.kuaishou.com/d/home/<USER>
def get_saber_probs(input_h, input_share, label_nums, name=''):
    prob_lst = []
    _name = name if name else 'saber'
    with tf.variable_scope(_name, reuse=tf.AUTO_REUSE):
        for i in range(label_nums):
            # [batch_size, h_size + share_size]
            input = tf.concat([input_h, input_share[i]], axis=1)
            input_size = input.get_shape().as_list()[-1]
            input_h_size = input_h.get_shape().as_list()[-1]
            w = variable_on_cpu('w_{}'.format(i), [input_size, input_h_size],
                                initializer=tf.random_normal_initializer(stddev=1.0 / math.sqrt(float(input_size))))
            b = variable_on_cpu('b_{}'.format(i), [input_h_size], initializer=tf.zeros_initializer)
            # [batch_size, input_h_size]
            input_h = tf.nn.relu(tf.add(tf.matmul(input, w), b))

            w_out = variable_on_cpu(
                'w_out_{}'.format(i), [input_h_size, 1],
                initializer=tf.random_normal_initializer(stddev=1.0 / math.sqrt(float(input_h_size))))
            b_out = variable_on_cpu('b_out_{}'.format(i), [1], initializer=tf.zeros_initializer)
            logit_i = tf.add(tf.matmul(input_h, w_out), b_out)
            prob_lst.append(tf.nn.sigmoid(logit_i))

    probs = tf.identity(tf.concat(prob_lst, 1), name='saber_sigmoid')
    return probs

################################################################################################################################
# leave-time 时间分桶处理函数
def define_labels(labels_ecpm, label_nums, bins):
    mock_bins = [1, ]
    mock_bins.extend(bins)
    mock_labels_idx = tf.cast(math_ops._bucketize(labels_ecpm, boundaries=mock_bins), tf.int64) - 1
    mock_labels_onehot = tf.reshape(tf.one_hot(mock_labels_idx, label_nums), (-1, label_nums))
    lower_triangular_ones = tf.linalg.band_part(tf.ones((label_nums, label_nums)), -1, 0, name='lower_triangular_ones')
    labels_multitask = tf.matmul(mock_labels_onehot, lower_triangular_ones)

    labels_idx = tf.cast(math_ops._bucketize(labels_ecpm, boundaries=bins), tf.int64)
    labels_onehot = tf.reshape(tf.one_hot(labels_idx, label_nums), (-1, label_nums))
    return labels_ecpm, labels_multitask, labels_idx, labels_onehot

def calc_bins_gap(bins_center):
    if isinstance(bins_center, list):
        bins_center_size = len(bins_center)
        bins_gap = [[bins_center[0], ], ]
        for i in range(bins_center_size - 1):
            bins_gap.append([bins_center[i + 1] - bins_center[i], ])
        return tf.constant(bins_gap, tf.float32)
    else:
        bins_center_size = bins_center.get_shape().as_list()[-1]
        bins_gap = [[bins_center[0], ], ]
        for i in range(bins_center_size - 1):
            bins_gap.append([bins_center[i + 1] - bins_center[i], ])
        return tf.concat(bins_gap, axis=0)
################################################################################################################################

def dense_load_handle(warmup_weight: dict, warmup_extra: dict, ps_weight: dict, ps_extra: dict, tf_weight: dict, load_option): 
    ''' 
    https://docs.corp.kuaishou.com/k/home/<USER>/fcAAXcP_sb-h0_8v1lEr7wIqa#section=h.jitvgok6c7vl
    - 参数一：warmup_weight，从base的model加载得到的weight，key为参数名，value为numpy形式的参数值 。 
    - 参数二：warmup_extra，从base的model加载得到的extra(optimizer 依赖参数)，key为参数名，value为numpy形式的参数值
    - 参数三：ps_weight，从参数服务器上拉取的weight，key为参数名，value为numpy形式的参数值
    - 参数四：ps_extra，从参数服务器上拉取的extra，key为参数名，value为numpy形式的参数值
    - 参数五：tf_weight，tensorflow本地通过初始化op生成的weight，key为参数名，value为numpy形式的参数值
    - 参数六：load_option，kai.load()的配置，包含加载参数的地址，加载模式等信息
    - 返回值一：weight(dict), 最终确定的weight组合，key为参数名，value为numpy形式的参数值
    - 返回值二：extra(dict), 最终确定的extra组合，key为参数名，value为numpy形式的参数值
    - 都是这种格式： {weight_name1 : np_array, weight_name2 : np_array, ... }
    '''

    import numpy as np

    weight = None
    extra = None
    dense_variable_nums = len(tf_weight)

    if warmup_weight is not None and len(warmup_weight) > 0:
        for var_name in list(warmup_weight):
            if var_name not in tf_weight: # 表示参数存在base模型，但新模型没有。即【删除参数】
                print("加载的 dense variable({}) 在运行时不存在，其值被忽略。".format(var_name))
                del warmup_weight[var_name]
                del warmup_extra[var_name]        

            elif warmup_weight[var_name].size != tf_weight[var_name].size: # base模型的参数维度和新模型不一样，即此参数被修改，需要额外处理。即【修改参数】
                if False:
                    print("加载的 dense variable({}) size ({} vs {}) 不匹配，其值被忽略".format(var_name, warmup_weight[var_name].shape, tf_weight[var_name].shape))
                    del warmup_weight[var_name]
                    del warmup_extra[var_name]
                else:
                    print("加载的 dense variable({}) size ({} vs {}) 不匹配: --> Padding".format(var_name, warmup_weight[var_name].shape, tf_weight[var_name].shape))
                    # 新增user 16个  (25125 -> 25381)
                    # base [corss_embedding, combine_embedding, dense_embedding] = [23876, 800, 449]  -->
                    # test [corss_embedding, combine_embedding, user_new_embedding, dense_embedding] = [23876, 800, 256, 449]
                    if var_name in ('LayerNorm/beta:0', 'LayerNorm/gamma:0'):
                        warmup_weight[var_name] = np.concatenate([warmup_weight[var_name][:3584],
                                                                 tf_weight[var_name][3584:3584+256+304],
                                                                 warmup_weight[var_name][3584:]
                        ], axis=0)

                        assert warmup_weight[var_name].size == tf_weight[var_name].size, "dense variable({}) size ({} vs {}) 不匹配".format(var_name, warmup_weight[var_name].shape, tf_weight[var_name].shape)

                    elif var_name == 'share_bottom_layer_0/w:0':
                        warmup_weight[var_name] = np.concatenate([warmup_weight[var_name][:23876+800, :], # base corss wise
                                                                  tf_weight[var_name][23876+800:23876+800+256+304, :],  # base dense wise   
                                                                  warmup_weight[var_name][23876+800:, :]   # init dense  wise
                        ], axis=0)

                        assert warmup_weight[var_name].size == tf_weight[var_name].size, "dense variable({}) size ({} vs {}) 不匹配".format(var_name, warmup_weight[var_name].shape, tf_weight[var_name].shape)
                    
                    elif var_name == "upper_layer_3/w:0":
                        warmup_weight[var_name] = np.concatenate([warmup_weight[var_name][:128, :],
                                                                  tf_weight[var_name][128:, :]
                        ], axis=0)
                        assert warmup_weight[var_name].size == tf_weight[var_name].size, "dense variable({}) size ({} vs {}) 不匹配".format(var_name, warmup_weight[var_name].shape, tf_weight[var_name].shape)
                        print("加载的 dense variable({}) 已部分热启动成功".format(var_name))
                        
                    else: 
                        print("加载的 dense variable({}) size ({} vs {}) 不匹配: --> 其值被忽略".format(var_name, warmup_weight[var_name].shape, tf_weight[var_name].shape))
                        del warmup_weight[var_name]
                        del warmup_extra[var_name]


        weight = warmup_weight
    else:
        weight = tf_weight # 冷启动。用tf初始化。若用 weight=ps_weight，表示weight用ps初始化。



    if warmup_extra is not None and len(warmup_extra) > 0: # 
        for var_name in list(warmup_extra):
            if var_name not in ps_extra:
                print("加载的 dense variable extra({}) 在运行时不存在，其值被忽略。".format(var_name))  # noqa
                del warmup_extra[var_name]
            elif warmup_extra[var_name].size != ps_extra[var_name].size:
                if False:
                    print("加载的 dense variable extra({}) size ({} vs {}) 不匹配，其值被忽略".format(var_name, warmup_extra[var_name].shape, ps_extra[var_name].shape))
                    del warmup_extra[var_name]

                else:
                    print("加载的 dense variable extra({}) size ({} vs {}) 不匹配: --> Padding".format(var_name, warmup_extra[var_name].shape, ps_extra[var_name].shape))
                    if var_name in ('LayerNorm/beta:0', 'LayerNorm/gamma:0'):
                        # extra 是展开的
                        warmup_extra[var_name] = warmup_extra[var_name].reshape((-1, ))
                        ps_extra[var_name]     = ps_extra[var_name].reshape((-1, ))

                        warmup_extra[var_name] = np.concatenate([warmup_extra[var_name][:3584],
                                                                 ps_extra[var_name][3584:3584+256+304],
                                                                 warmup_extra[var_name][3584:]
                        ], axis=0)

                        warmup_extra[var_name] = warmup_extra[var_name].reshape((-1, ))
                        assert warmup_extra[var_name].size == ps_extra[var_name].size, "dense variable extra({}) size ({} vs {}) 不匹配".format(var_name, warmup_extra[var_name].shape, ps_extra[var_name].shape)
                    
                    elif var_name == 'share_bottom_layer_0/w:0':
                        # extra 是展开的
                        warmup_extra[var_name] = warmup_extra[var_name].reshape((-1, 1024))
                        ps_extra[var_name]     = ps_extra[var_name].reshape((-1, 1024))

                        # 新增reco_user 1个, user 5个, photo 1个, combine 2个, 共计9个)  (25125 -> 25269)
                        # base [corss_embedding, combine_embedding, dense_embedding] = [23876, 800, 449]  -->
                        # test [corss_embedding, combine_embedding, user_new_embedding, dense_embedding] = [23876, 800, 256, 449]
                        warmup_extra[var_name] = np.concatenate([warmup_extra[var_name][:23876+800, :], # base corss wise
                                                                  ps_extra[var_name][23876+800:23876+800+256+304, :],  # base dense wise   
                                                                  warmup_extra[var_name][23876+800:, :]   # init dense  wise
                        ], axis=0)

                        warmup_extra[var_name] = warmup_extra[var_name].reshape((-1, ))

                        assert warmup_extra[var_name].size == ps_extra[var_name].size, "dense variable extra({}) size ({} vs {}) 不匹配".format(var_name, warmup_extra[var_name].shape, ps_extra[var_name].shape)

                    elif var_name == "upper_layer_3/w:0":
                        warmup_extra[var_name] = warmup_extra[var_name].reshape((-1, 2))
                        ps_extra[var_name]     = ps_extra[var_name].reshape((-1, 2))
                        warmup_extra[var_name] = np.concatenate([warmup_extra[var_name][:128, :],
                                                                 ps_extra[var_name][128:, :]
                        ], axis=0)
                        warmup_extra[var_name] = warmup_extra[var_name].reshape((-1, ))
                        assert warmup_extra[var_name].size == ps_extra[var_name].size, "dense variable extra({}) size ({} vs {}) 不匹配".format(var_name, warmup_extra[var_name].shape, ps_extra[var_name].shape)
                        print("加载的 dense variable extra ({}) 已部分热启动成功".format(var_name))

                    else: 
                        print("加载的 dense variable extra({}) size ({} vs {}) 不匹配: --> 其值被忽略".format(var_name, warmup_extra[var_name].shape, ps_extra[var_name].shape))
                        del warmup_extra[var_name]


        extra = warmup_extra
    else:
        extra = ps_extra

    if len(weight) < dense_variable_nums: # 不存在base模型里，但存在新模型里，即【新增参数】
        for var_name, var in tf_weight.items():
            if var_name not in weight: # tf_weight 是新模型的所有w，而weight是前面一项项放入的。这里表示新模型有而旧模型没有。
                if var_name in ("if_leave_layer_1/w:0", "leave_time_seq_layer_1/w:0"):
                    weight[var_name] = weight["leavetime_layer_1/w:0"]
                if var_name in ("if_leave_layer_1/b:0", "leave_time_seq_layer_1/b:0"):
                    weight[var_name] = weight["leavetime_layer_1/b:0"]
                if var_name in ("if_leave_layer_1/LayerNorm/beta:0", "leave_time_seq_layer_1/LayerNorm/beta:0"):
                    weight[var_name] = weight["leavetime_layer_1/LayerNorm/beta:0"]
                if var_name in ("if_leave_layer_1/LayerNorm/gamma:0", "leave_time_seq_layer_1/LayerNorm/gamma:0"):
                    weight[var_name] = weight["leavetime_layer_1/LayerNorm/gamma:0"]
                if var_name in ("if_leave_layer_2/w:0", "leave_time_seq_layer_2/w:0"):
                    weight[var_name] = weight["leavetime_layer_2/w:0"]
                if var_name in ("if_leave_layer_2/b:0", "leave_time_seq_layer_2/b:0"):
                    weight[var_name] = weight["leavetime_layer_2/b:0"]
                if var_name in ("if_leave_layer_2/LayerNorm/beta:0", "leave_time_seq_layer_2/LayerNorm/beta:0"):
                    weight[var_name] = weight["leavetime_layer_2/LayerNorm/beta:0"]
                if var_name in ("if_leave_layer_2/LayerNorm/gamma:0", "leave_time_seq_layer_2/LayerNorm/gamma:0"):
                    weight[var_name] = weight["leavetime_layer_2/LayerNorm/gamma:0"]
                if var_name in ("if_leave_layer_3/w:0"):
                    weight[var_name] = weight["leavetime_layer_3/w:0"]
                if var_name in ("if_leave_layer_3/b:0"):
                    weight[var_name] = weight["leavetime_layer_3/b:0"]
            
    if len(extra) < dense_variable_nums: # 不存在base模型里，但存在新模型里，即【新增extra参数】
        for var_name, var in ps_extra.items():
            if var_name not in extra: # ps_extra是新模型的所有extra，而extra是前面一项项放入的
                if var_name in ("if_leave_layer_1/w:0", "leave_time_seq_layer_1/w:0"):
                    extra[var_name] = extra["leavetime_layer_1/w:0"]
                if var_name in ("if_leave_layer_1/b:0", "leave_time_seq_layer_1/b:0"):
                    extra[var_name] = extra["leavetime_layer_1/b:0"]
                if var_name in ("if_leave_layer_1/LayerNorm/beta:0", "leave_time_seq_layer_1/LayerNorm/beta:0"):
                    extra[var_name] = extra["leavetime_layer_1/LayerNorm/beta:0"]
                if var_name in ("if_leave_layer_1/LayerNorm/gamma:0", "leave_time_seq_layer_1/LayerNorm/gamma:0"):
                    extra[var_name] = extra["leavetime_layer_1/LayerNorm/gamma:0"]
                if var_name in ("if_leave_layer_2/w:0", "leave_time_seq_layer_2/w:0"):
                    extra[var_name] = extra["leavetime_layer_2/w:0"]
                if var_name in ("if_leave_layer_2/b:0", "leave_time_seq_layer_2/b:0"):
                    extra[var_name] = extra["leavetime_layer_2/b:0"]
                if var_name in ("if_leave_layer_2/LayerNorm/beta:0", "leave_time_seq_layer_2/LayerNorm/beta:0"):
                    extra[var_name] = extra["leavetime_layer_2/LayerNorm/beta:0"]
                if var_name in ("if_leave_layer_2/LayerNorm/gamma:0", "leave_time_seq_layer_2/LayerNorm/gamma:0"):
                    extra[var_name] = extra["leavetime_layer_2/LayerNorm/gamma:0"]
                if var_name in ("if_leave_layer_3/w:0"):
                    extra[var_name] = extra["leavetime_layer_3/w:0"]
                if var_name in ("if_leave_layer_3/b:0"):
                    extra[var_name] = extra["leavetime_layer_3/b:0"]

    if len(weight) < dense_variable_nums: # 不存在base模型里，但存在新模型里，即【新增参数】
        for var_name, var in tf_weight.items():
            if var_name not in weight: # tf_weight 是新模型的所有w，而weight是前面一项项放入的。这里表示新模型有而旧模型没有。
                weight[var_name] = var # 表示：将新增的var_name 的数值arr赋予给 weight。 此处可改为自定义初始化后的arr
                # 或者使用 numpy 自己控制数值如何初始化
                # weight[var_name] = np.array()
            
    if len(extra) < dense_variable_nums: # 不存在base模型里，但存在新模型里，即【新增extra参数】
        for var_name, var in ps_extra.items():
            if var_name not in extra: # ps_extra是新模型的所有extra，而extra是前面一项项放入的
                extra[var_name] = var # 表示：将新增的var_name 的数值arr赋予给 extra，此处可改为自定义初始化后的arr


    assert len(weight) == dense_variable_nums
    assert len(extra) == dense_variable_nums

    return weight, extra # 返回让框架来赋值


# 还需要把save hdfs中的done list删 或者配置 force_load_init_model=true，才会走init的逻辑
kai.set_load_dense_func(dense_load_handle)

class HashDnnModel(object):
    def __init__(self):
        klearn_ops = []
        klearn_conf = type('', (), {})()
        klearn_conf.kafka_tag = ['search_lps_filter:item_impression']
        klearn_conf.user_name = 'zhengchaofan'
        klearn_conf.extractor_type = 'KlearnCofeaSampleExtractor'
        klearn_conf.feature_text = './kai_feature.txt'
        klearn_conf.batch_size = 1024
        klearn_conf.klearn_conf = type('',(),{})()
        klearn_conf.klearn_conf.base_lr = 0.1
        klearn_conf.klearn_conf.batch_size = 1024
        klearn_conf.klearn_conf.decay = 0.0
        klearn_conf.klearn_conf.dnn_net_size = '1024,256,128,2'
        klearn_conf.klearn_conf.embedding_size = 16
        klearn_conf.klearn_conf.eps = 0.0001
        klearn_conf.klearn_conf.exclude_dense_set = set()
        klearn_conf.klearn_conf.fields = 259
        # klearn_conf.klearn_conf.input_dense_total_size = 449
        # klearn_conf.klearn_conf.input_dense_user_count = 7
        # klearn_conf.klearn_conf.input_sparse_total_size = 3584
        klearn_conf.klearn_conf.input_sparse_user_count = 127
        klearn_conf.klearn_conf.item_type = 'AD_DSP'
        klearn_conf.klearn_conf.l2 = 0.0
        klearn_conf.klearn_conf.neg_weight = '1'
        klearn_conf.klearn_conf.ps_hash = 0
        klearn_conf.klearn_conf.tab = ''
        klearn_conf.klearn_conf.topic = 'ad_kafka_feature_dsp_click_lps'
        klearn_conf.klearn_conf.topic_id = 'ad_kafka_feature_dsp_click_lps'
        klearn_conf.klearn_conf.train_loss_diff = '0.5'
        klearn_conf.klearn_conf.use_bn = False
        klearn_conf.klearn_conf.version = 3
        klearn_conf.batch_size = 1024
        klearn_conf.model_name = 'dsp_lps_search_kai2'
        from kai.tensorflow.config.ad_config.ktrain.klearn_utils.klearn_config import parse_klearn_feature
        parse_klearn_feature(klearn_conf)
        user_conf = klearn_conf
        self._config = klearn_conf
        self._klearn_ops = klearn_ops
        self._dnn_net_size = [int(x) for x in self._config.klearn_conf.dnn_net_size.split(',') if len(x) > 0]
        self._config.klearn_conf.user_feature_size = 127
        self._config.klearn_conf.photo_feature_size = 47
        self._config.klearn_conf.combine_feature_size = 50
        self._config.klearn_conf.share_num = 1
        self._config.klearn_conf.neg_weight = float(self._config.klearn_conf.neg_weight)
        self._last_result = {}
        self.set_t = True
        if self.set_t:
            self.use_bn = False
            self.ln  = True
            self.trainable_task_weight = True
        else:
            self.use_bn = True
            self.ln = False
            self.trainable_task_weight = False
        self.weight_ctr = 0.2  # ctr
        self.weight_ctcvr = 1.0  # ctcvr
        self.lookup_tbl = get_lookup_tbl_with_neg(9, self._config.klearn_conf.neg_weight)
        self.hook_inputs = {}
        self.local_step = 0
        self.real_mean_acc = 0.0
        self.prob_mean_acc = 0.0
        # leave-time标签配置
        # 标签配置
        self.bins_center = [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 52, 62, 72, 82, 92, 102, 122, 142, 162, 182, 222, 302, 402, 502, 602, 802, 1200, 1800]
        self.bins = [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 47, 57, 67, 77, 87, 97, 112, 132, 152, 172, 202, 262, 352, 452, 552, 702, 1001, 1800]
        self.label_nums = len(self.bins_center)

    def GetWriteTensor(self):
        return self.hook_inputs
    def write_hook(self, res):
        self.real_mean_acc += res['ctcvr_real_mean']
        self.prob_mean_acc += res['ctcvr_prob_mean']
        if self.local_step % 2000 == 0:
            print('================= debug_info local_step {} =================\n'.format(self.local_step))
            keys = list(self.GetWriteTensor().keys())
            if 'loss' in res:
                keys.append('loss')
            for key in keys:
                if key not in res:
                    continue
                print(key + ':', res[key])
            print('prob_mean_acc' + ':', self.prob_mean_acc / self.local_step)
            print('real_mean_acc' + ':', self.real_mean_acc / self.local_step)
        self.local_step += 1

    def get_learning_rate(self, total_train_step, cur_step, base_lr=0.007):
        if cur_step < total_train_step:
            lr = base_lr * (1.1 - math.exp(-cur_step * 2.33 / total_train_step))
            if lr > base_lr:
                lr = base_lr
            return lr
        return base_lr
    def fc(self, input, input_size, layer_size, i, is_train, is_cpu_ps):
        weight_name = 'w'
        w = data_ops.variable_on_cpu(weight_name, [input_size, layer_size],
                                     initializer=tf.random_normal_initializer(
                                         stddev=1.0 / math.sqrt(float(input_size))),
                                     is_cpu_ps=is_cpu_ps)
        bias_name = 'b'
        b = data_ops.variable_on_cpu(bias_name, [layer_size],
                                     initializer=tf.zeros_initializer,
                                     is_cpu_ps=is_cpu_ps)
        logger.info("%s length=%d * %d" % (w.name, input_size, layer_size))
        logger.info("%s length=%d" % (b.name, layer_size))
        o1 = tf.add(tf.matmul(input, w), b)
        if i != len(self._dnn_net_size) - 1:
            if self.use_bn:
                # o1 = BatchNorm(# o1, self.is_train_pl, 'bn')
                logger.info('bn in the {} layer'.format('share_bottom_layer_' + str(i)))
                o1 = kai.batch_norm(o1, 'bn')
            if self.ln:
                logger.info('ln in the {} layer'.format('share_bottom_layer_' + str(i)))
                o1 = tf.contrib.layers.layer_norm(o1)
                # o1 = vo_ln(o1)
            o = tf.nn.relu(o1)
        else:
            o = o1
        return o
    def fc1(self, input, input_size, layer_size, is_output_layer, score_name='mlp'):
        with tf.variable_scope(score_name):
            weight_name = 'w'
            w = tf.get_variable(weight_name, [input_size, layer_size],
                                initializer=tf.random_normal_initializer(
                                    stddev=1.0 / math.sqrt(float(input_size))),
                                trainable=True)
            bias_name = 'b'
            b = tf.get_variable(bias_name, [layer_size],
                                initializer=tf.zeros_initializer,
                                trainable=True)

            if is_output_layer:
                o = tf.add(tf.matmul(input, w), b)
            else:
                o1 = tf.add(tf.matmul(input, w), b)
                if self.use_bn:
                    # o1 = BatchNorm(# o1, self.is_train_pl, 'bn')
                    logger.info('bn in the single mlp layer')
                    o1 = kai.batch_norm(o1, 'bn')
                if self.ln:
                    logger.info('bn in the single mlp layer')
                    o1 = tf.contrib.layers.layer_norm(o1)
                    # o1 = vo_ln(o1)
                o = tf.nn.relu(o1)
        return o
    def GetMetric(self):
        return self.eval_targets
    def GetPlaceHolder(self):
        return {"is_train": self.is_train_pl}
    def GetAUC(self):
        return {"auc": self.auc}
    def kai_v2_model_def(self):
        from kai.tensorflow.nn import ParamAttr

        is_cpu_ps = False 
        is_train = True 
        hooks = []
        exclude_dense = []
        default_param_attr = ParamAttr(initializer=kai.nn.UniformInitializer(0.01),
                                      access_method=kai.nn.ProbabilityAccess(100),
                                      recycle_method=kai.nn.UnseendaysRecycle(3650000, 0.0, False))
        kai.nn.set_default_param_attr(default_param_attr)
        sparse = kai.new_embedding('sparse', dim=16, slots=list(range(1, 211)), expand=None)
        add_sea = kai.new_embedding('add_sea', dim=16, slots=list(range(211, 225)), expand=None)
        # 2024.08.11 新增user_num: 11
        add_sea_v2 = kai.new_embedding('add_sea_v2', dim=16, slots=list(range(225, 241)), expand=None)
        # 2024.08.21 新增sdpa + 信息流 top 特征
        add_sea_v3 = kai.new_embedding('add_sea_v3', dim=16, slots=list(range(241, 260)), expand=None)
        ExtractDenseHour = kai.get_dense_fea('ExtractDenseHour', dim=64)
        ExtractUserAdLpsNumExtendEcomDense = kai.get_dense_fea('ExtractUserAdLpsNumExtendEcomDense', dim=2)
        ExtractUserRecallTargetInfo = kai.get_dense_fea('ExtractUserRecallTargetInfo', dim=16)
        ExtractUserAdItemClickNumExtendEcomDense = kai.get_dense_fea('ExtractUserAdItemClickNumExtendEcomDense', dim=2)
        ExtractUserTextFeatureBertConv = kai.get_dense_fea('ExtractUserTextFeatureBertConv', dim=32)
        ExtractUserEcomTextFeatureCtrConv = kai.get_dense_fea('ExtractUserEcomTextFeatureCtrConv', dim=32)
        ExtractRecoSlideFmRe = kai.get_dense_fea('ExtractRecoSlideFmRe', dim=128)
        ExtractPhotoAutoCpaBidNebula = kai.get_dense_fea('ExtractPhotoAutoCpaBidNebula', dim=1)
        ExtractPhotoCpaBid = kai.get_dense_fea('ExtractPhotoCpaBid', dim=1)
        ExtractPhotoEmbeddingFeature2 = kai.get_dense_fea('ExtractPhotoEmbeddingFeature2', dim=64)
        ExtractPhotoVisionFeatureCoverCTR = kai.get_dense_fea('ExtractPhotoVisionFeatureCoverCTR', dim=32)
        ExtractAdLpsPosterRate = kai.get_dense_fea('ExtractAdLpsPosterRate', dim=12)
        ExtractEcomProductCoverEmb = kai.get_dense_fea('ExtractEcomProductCoverEmb', dim=32)
        ExtractNumDense = kai.get_dense_fea('ExtractNumDense', dim=16)
        ExtractMatchDenseNum14Days = kai.get_dense_fea('ExtractMatchDenseNum14Days', dim=15)
        dense = tf.concat([ExtractDenseHour, ExtractUserAdLpsNumExtendEcomDense, ExtractUserRecallTargetInfo, ExtractUserAdItemClickNumExtendEcomDense, ExtractUserTextFeatureBertConv, ExtractUserEcomTextFeatureCtrConv, ExtractRecoSlideFmRe, ExtractPhotoAutoCpaBidNebula, ExtractPhotoCpaBid, ExtractPhotoEmbeddingFeature2, ExtractPhotoVisionFeatureCoverCTR, ExtractAdLpsPosterRate, ExtractEcomProductCoverEmb, ExtractNumDense, ExtractMatchDenseNum14Days], axis=1)
        dnn_input = tf.concat([sparse, add_sea, add_sea_v2, add_sea_v3, dense], axis=1)
        dnn_input_list = [sparse, add_sea, add_sea_v2, add_sea_v3, dense]
        labels = kai.get_label('label', from_sparse=True)
        labels = tf.cast(labels, tf.int32)
        #下面的block_data、 non_block_data只是为了完成兼容，用户可以自行修改
        self.block_data = [type('', (), dict(output=input))() for input in dnn_input_list]
        self.non_block_data = type('', (), dict(label_pl=labels))()
        train_loss = self.inference(dnn_input, labels, is_cpu_ps, is_train, hooks, exclude_dense)
        sparse_optimizer = kai.optimizer.AdagradW(learning_rate=0.1, eps=0.0001, decay=0.0, l2=0.0, version=3)
        dense_optimizer = kai.optimizer.AdagradW(learning_rate=0.1, eps=0.0001, decay=0.0, l2=0.0, version=2)
        
        # 为了兼容kai python，sparse_loss乘上batch_size * worker_num；dense_loss乘上batch_size；
        sparse_optimizer.minimize(train_loss * tf.cast(tf.shape(labels)[0], tf.float32) * kai.worker_num(), var_list=kai.get_collection(kai.GraphKeys.EMBEDDING_INPUT))
        dense_optimizer.minimize(train_loss * tf.cast(tf.shape(labels)[0], tf.float32), var_list=kai.get_collection(kai.GraphKeys.TRAINABLE_VARIABLES))
        # sparse_optimizer.minimize(train_loss, var_list=kai.get_collection(kai.GraphKeys.EMBEDDING_INPUT))
        # dense_optimizer.minimize(train_loss / kai.worker_num(), var_list=kai.get_collection(kai.GraphKeys.TRAINABLE_VARIABLES))
        return {'optimizer': [sparse_optimizer, dense_optimizer], 'metrics': self.eval_targets}

    def extract_search_features(self, sparse_input, add_sea_input, add_sea_v2_input, add_sea_v3_input):
        """从不同的 embedding 中按照slots提取搜索相关特征"""
        embedding_size = self._config.klearn_conf.embedding_size  # 16

        logger.info("Extracting search features by slots from different embeddings")

        search_feature_slices = []

        # 根据feature_map_for_ps.txt中的所有搜索特征slots (remap_slot)
        search_slots = [
            94,   # ExtractSearchQuerySource
            96,   # ExtractSearchReferPhotoId
            109,  # ExtractSearchQueryCategoryCalss3
            115,  # ExtractSearchQueryCategoryCalss2
            116,  # ExtractQuery
            117,  # ExtractQuerytoken
            118,  # ExtractSearchFromPage
            119,  # ExtractSearchPosId
            218,  # ExtractSearchEnterSource
            160,  # ExtractSearchRecallMatchtype
            161,  # ExtractSearchRecallRelevance
            162,  # ExtractSearchRecallStrategy
            163,  # ExtractSearchRecallStrategyType
            164,  # ExtractSearchRewriteQuery
            165,  # ExtractSearchQrScore
            166,  # ExtractSearchExtendType
            173,  # ExtractSearchKboxType
            195,  # ExtractSearchPhotoPname
            196,  # ExtractSearchPhotoPname2
            197,  # ExtractSearchPhotoAsr
            198,  # ExtractSearchPhotoAsr2
            199,  # ExtractSearchPhotoCname
            200,  # ExtractSearchPhotoCname2
            201,  # ExtractSearchPhotoDescription
            202,  # ExtractSearchPhotoDescription2
            203,  # ExtractSearchPhotoOcr
            204,  # ExtractSearchPhotoOcr2
            205,  # ExtractSearchPhotoOcrTitle
            206,  # ExtractSearchPhotoOcrTitle2
            207,  # ExtractSearchPhotoSlogan
            208,  # ExtractSearchPhotoSlogan2
            219,  # ExtractSearchBidword
            222,  # ExtractSearchParserTextTokenV1
            223,  # ExtractSearchParserTextV1
            224,  # ExtractSearchQueryCombineMatchNum
        ]

        logger.info("Extracting {} search features by slots".format(len(search_slots)))

        for slot_id in search_slots:
            # 根据slot确定在哪个embedding中
            if 1 <= slot_id <= 210:
                # 在sparse embedding中
                start_idx = (slot_id - 1) * embedding_size
                feature_slice = tf.slice(sparse_input, [0, start_idx], [-1, embedding_size])
            elif 211 <= slot_id <= 224:
                # 在add_sea embedding中
                start_idx = (slot_id - 211) * embedding_size
                feature_slice = tf.slice(add_sea_input, [0, start_idx], [-1, embedding_size])
            elif 225 <= slot_id <= 240:
                # 在add_sea_v2 embedding中
                start_idx = (slot_id - 225) * embedding_size
                feature_slice = tf.slice(add_sea_v2_input, [0, start_idx], [-1, embedding_size])
            elif 241 <= slot_id <= 259:
                # 在add_sea_v3 embedding中
                start_idx = (slot_id - 241) * embedding_size
                feature_slice = tf.slice(add_sea_v3_input, [0, start_idx], [-1, embedding_size])
            else:
                logger.error("Unknown slot_id: {}".format(slot_id))
                continue

            search_feature_slices.append(feature_slice)

            

        # 拼接所有搜索特征
        search_features = tf.concat(search_feature_slices, axis=1)
        total_search_features = len(search_slots)
        expected_dim = total_search_features * embedding_size
        logger.info("search_features shape: {}, total features: {}, expected dim: {}".format(
            search_features.get_shape(), total_search_features, expected_dim))

        return search_features

    def inference(self, dnn_input, labels, is_cpu_ps, is_train, hooks, exclude_dense):
        self.is_train_pl = kai.get_train_placeholder()
        labels = tf.reshape(tf.cast(labels, tf.int32), [-1])
        sparse_units = len(self._config.klearn_conf.fields) * self._config.klearn_conf.embedding_size
        dense_units = sum(self._config.klearn_conf.real_dense_fields)

        sparse_input = self.block_data[0].output
        
        add_sea_input = self.block_data[1].output
        logger.info("add_sea_input shape: {}".format(add_sea_input.get_shape()))

        add_sea_v2_input = self.block_data[2].output
        logger.info("add_sea_v2_input shape: {}".format(add_sea_v2_input.get_shape()))

        add_sea_v3_input = self.block_data[3].output
        logger.info("add_sea_v3_input shape: {}".format(add_sea_v3_input.get_shape()))
        
        dense_input = self.block_data[4].output
        logger.info("dense_input shape: {}".format(dense_input.get_shape()))

        dnn_input = tf.concat([sparse_input, add_sea_input, add_sea_v2_input, add_sea_v3_input, dense_input], axis = 1)
        input_size = dnn_input.get_shape().as_list()[1]
        logger.info('sparse_units, {0}, dense_units, {1}, input_size, {2}'.format(sparse_units, dense_units, input_size))

        input = dnn_input
        logger.info(
            'sparse_units, {0}, dense_units, {1}, input_size, {2}'.format(sparse_units, dense_units, input_size))
        logger.info("dnn_input shape: {}".format(dnn_input.get_shape()))
        labels = tf.Print(labels, [labels], message='raw labels: ', first_n=3, summarize=1000)
        labels = tf.reshape(labels, [-1])
        logger.info("origin_1 label shape: {}".format(labels.get_shape()))
        # 新label抽取逻辑: 原始label = leave_time * 1000 + 其余label集合, 因此进行反向抽取
        label_leave_time = tf.floordiv(labels, 1000)
        label_leave_time = tf.cast(tf.clip_by_value(tf.math.abs(label_leave_time), 0, 1800), dtype=tf.int32)
        label_if_leave = tf.where(tf.not_equal(label_leave_time, 0), tf.ones_like(label_leave_time), tf.zeros_like(label_leave_time))

        label_leave_time, label_leave_time_multitask, label_leave_time_idx, labels_leave_time_onehot = \
            define_labels(label_leave_time, self.label_nums, self.bins)
        label_bucket_anchor = tf.fill(tf.shape(label_leave_time_idx), tf.constant(2, dtype=tf.int32))  # 从2号桶开始进行loss加权

        leave_time_bins_center = tf.reshape(tf.constant(self.bins_center, tf.float32), [self.label_nums, ])
        leave_time_bins_gap = calc_bins_gap(leave_time_bins_center)
        leave_time_bins_gap = tf.reshape(leave_time_bins_gap, [self.label_nums, -1])

        labels = tf.math.mod(labels, 1000)
        tbl = self.lookup_tbl
        if not is_train:  # sample_weight = 1 in validation
            for i in range(len(tbl)):
                tbl[i][-1] = 1.
        labels = tf.gather(tbl, labels)
        logger.info("origin_2 label shape: {}".format(labels.get_shape()))
        labels = tf.Print(labels, [labels], message='converted labels: ', first_n=3, summarize=2000)

         # 搜索 label 处理
        label_search = labels[:, 0]
        search_cnt = tf.count_nonzero(label_search)
        MIN_CNT = 5
        label_search = tf.where(tf.greater(search_cnt, MIN_CNT), label_search, tf.ones_like(label_search))
        label_feed = tf.where(tf.greater(search_cnt, MIN_CNT), 1 - label_search, tf.ones_like(label_search))
        # label_feed = 1 - label_search
        label_search = tf.cast(label_search, tf.float32)
        label_feed = tf.cast(label_feed, tf.float32)

        label_ctr = tf.cast(labels[:, 1], tf.int32)
        label_ctcvr = tf.cast(labels[:, 2], tf.int32) # lps
        label_pxr = tf.cast(labels[:, 3], tf.int32)
        label_playend = tf.cast(labels[:, 4], tf.int32)
        label_approximate_purchase = tf.cast(labels[:, 5], tf.int32)
        label_leave_time_greater_one = tf.cast(labels[:, 7], tf.int32)
        label_ad_like = tf.cast(labels[:, 8], tf.int32)

        sample_weight = labels[:, -1]
        n_samples = tf.reduce_sum(sample_weight)
        n_samples = tf.Print(n_samples,[n_samples], message='n_samples: ', first_n=3, summarize=2000)

        n_samples_feed = tf.reduce_sum(sample_weight * label_feed)
        n_samples_feed = tf.Print(n_samples_feed,[n_samples_feed], message='n_samples_feed: ', first_n=3, summarize=2000)

        n_samples_search = tf.reduce_sum(sample_weight * label_search)
        n_samples_search = tf.Print(n_samples_search,[n_samples_search], message='n_samples_search: ', first_n=3, summarize=2000)

        n_samples_valid_leavetime = tf.reduce_sum(sample_weight * tf.cast(label_if_leave, tf.float32))

        if self.use_bn:
            logger.info('bn in the embedding layer')
            dnn_input = kai.batch_norm(dnn_input, 'bn')
        if self.ln:
            logger.info('ln in the embedding layer')
            dnn_input = tf.contrib.layers.layer_norm(dnn_input)
            # dnn_input = vo_ln(dnn_input)
        # cross
        user_units = self._config.klearn_conf.user_feature_size * self._config.klearn_conf.embedding_size
        photo_units = self._config.klearn_conf.photo_feature_size * self._config.klearn_conf.embedding_size
        combine_units = self._config.klearn_conf.combine_feature_size * self._config.klearn_conf.embedding_size
        cross_units = (self._config.klearn_conf.user_feature_size * self._config.klearn_conf.photo_feature_size) * self._config.klearn_conf.embedding_size
        #dnn_input = dnn_input/1.1
        embedding = tf.slice(dnn_input, [0, 0], [-1, sparse_units])
        dense = tf.slice(dnn_input, [0, sparse_units], [-1, dense_units])
        logger.info("sparse embedding shape: {}".format(embedding.get_shape()))
        logger.info("dense embedding shape: {}".format(dense.get_shape()))

        # migrate 代码 add by zhouxuan
        base_user_count = 119
        base_combine_count = 44
        extra_user_count = 8
        extra_combine_count = 6

        extra_user_v2_count = 16
        extra_reco_user_v3_count = 1
        extra_user_v3_count = 15
        extra_photo_v3_count = 1
        extra_combine_v3_count = 2

        base_user_units = base_user_count * self._config.klearn_conf.embedding_size
        extra_user_units = extra_user_count * self._config.klearn_conf.embedding_size
        base_combine_units = base_combine_count * self._config.klearn_conf.embedding_size
        extra_combine_units = extra_combine_count * self._config.klearn_conf.embedding_size

        extra_user_v2_units = extra_user_v2_count * self._config.klearn_conf.embedding_size

        extra_reco_user_v3_units = extra_reco_user_v3_count * self._config.klearn_conf.embedding_size
        extra_user_v3_units = extra_user_v3_count * self._config.klearn_conf.embedding_size
        extra_photo_v3_units = extra_photo_v3_count * self._config.klearn_conf.embedding_size
        extra_combine_v3_units = extra_combine_v3_count * self._config.klearn_conf.embedding_size
        logger.info("label_ctcvr shape: {}".format(label_ctcvr.get_shape()))
        logger.info("base user units {}, extra user units {}, combine units {}, extra combine units {}, photo units {}".format(
            base_user_units, extra_user_units, base_combine_units, extra_combine_units, photo_units))
        
        # 目前embedding的拼接方式 base_user + base_photo + base_combine + extra_user + extra_combine + dense
        # 0 ~ base_user_count 的 user 特征
        base_user_embedding = tf.slice(
            embedding, [0, 0], [-1, base_user_units])

        base_photo_embedding = tf.slice(
            embedding, [0, base_user_units], [-1, photo_units])
        base_combine_embedding = tf.slice(
            embedding, [0, base_user_units + photo_units], [-1, base_combine_units])
        extra_user_embedding = tf.slice(embedding, [
            0, base_user_units + photo_units + base_combine_units], [-1, extra_user_units])
        extra_combine_embedding = tf.slice(embedding, [
            0, base_user_units + photo_units + base_combine_units + extra_user_units], [-1, extra_combine_units])
        extra_user_v2_embedding = tf.slice(embedding, [
            0, base_user_units + photo_units + base_combine_units + extra_user_units + extra_combine_units], [-1, extra_user_v2_units])
        # extra_v3 特征切分
        extra_reco_user_v3_embedding = tf.slice(embedding, [
            0, base_user_units + photo_units + base_combine_units + extra_user_units + extra_combine_units + extra_user_v2_units], [-1, extra_reco_user_v3_units])
        extra_user_v3_embedding = tf.slice(embedding, [
            0, base_user_units + photo_units + base_combine_units + extra_user_units + extra_combine_units + extra_user_v2_units + extra_reco_user_v3_units], [-1, extra_user_v3_units])
        extra_photo_v3_embedding = tf.slice(embedding, [
            0,  base_user_units + photo_units + base_combine_units + extra_user_units + extra_combine_units + extra_user_v2_units + extra_reco_user_v3_units + extra_user_v3_units], [-1, extra_photo_v3_units])
        extra_combine_v3_embedding = tf.slice(embedding, [
            0, base_user_units + photo_units + base_combine_units + extra_user_units + extra_combine_units + extra_user_v2_units + extra_reco_user_v3_units + extra_user_v3_units + extra_photo_v3_units], [-1, extra_combine_v3_units])

        logger.info("base_user_embedding shape: {}".format(base_user_embedding.get_shape()))
        logger.info("base_photo_embedding shape: {}".format(base_photo_embedding.get_shape()))
        logger.info("base_combine_embedding shape: {}".format(base_combine_embedding.get_shape()))
        logger.info("extra_user_embedding shape: {}".format(extra_user_embedding.get_shape()))
        logger.info("extra_combine_embedding shape: {}".format(extra_combine_embedding.get_shape()))

        embedding_user = tf.concat([base_user_embedding, extra_user_embedding], axis = 1)
        embedding_photo = base_photo_embedding
        embedding_combine = tf.concat([base_combine_embedding, extra_combine_embedding], axis = 1)
        logger.info("embedding_user shape: {}".format(embedding_user.get_shape()))
        logger.info("embedding_photo shape: {}".format(embedding_photo.get_shape()))
        logger.info("embedding_combine shape: {}".format(embedding_combine.get_shape()))

        rep_user = tf.constant(np.tile(np.eye(self._config.klearn_conf.embedding_size), self._config.klearn_conf.photo_feature_size),
                               dtype=tf.float32)
        # user_f = 80, photo_f = 20
        embedding_user = tf.reshape(embedding_user, [-1, self._config.klearn_conf.user_feature_size, self._config.klearn_conf.embedding_size])
        embedding_user = tf.matmul(embedding_user, rep_user)  # 1024*80*16 x 16*320 = 1024*80*320
        embedding_photo = tf.reshape(embedding_photo, [-1, 1, photo_units])
        embedding_cross = tf.multiply(embedding_user, embedding_photo)  # 1024*80*20 *16
        w_1 = tf.constant(get_sub_matrix(self._config.klearn_conf.photo_feature_size), dtype=tf.float32)  # 16*16
        embeddding_cross_8sum = tf.matmul(embedding_cross, w_1)  # 1024*80*320   x 320*160 = 1024*80*160
        embedding_cross = tf.reshape(embeddding_cross_8sum, [-1, cross_units // 4])
        dnn_input = tf.concat([embedding_cross, embedding_combine, extra_user_v2_embedding, extra_reco_user_v3_embedding, extra_user_v3_embedding, extra_photo_v3_embedding, extra_combine_v3_embedding, dense], 1)

        logger.info("embedding_cross shape: {}".format(embedding_cross.get_shape()))
        logger.info("embedding_combine shape: {}".format(embedding_combine.get_shape()))
        logger.info("embedding_user new shape: {}".format(extra_user_v2_embedding.get_shape()))
        logger.info("embedding_dense shape: {}".format(dense.get_shape()))
        input_size = dnn_input.get_shape().as_list()[1]
        input = dnn_input
        # share bottom
        for i in range(self._config.klearn_conf.share_num):
            with tf.variable_scope("share_bottom_layer_{}".format(i)):
                layer_size = self._dnn_net_size[i]
                input = self.fc(input, input_size, layer_size, i, is_train, is_cpu_ps)
                input_size = layer_size

        leave_input = other_input = pxr_input = ped_input = purchase_input = leave_greater_one_input = if_leave_input = input
        leave_input_size = other_input_size = pxr_input_size = ped_input_size = purchase_input_size = leave_greater_one_input_size = if_leave_input_size = input_size

        # pxr_head
        for i in range(self._config.klearn_conf.share_num, len(self._dnn_net_size)):
            with tf.variable_scope("pxr_layer_{}".format(i)):
                layer_size = self._dnn_net_size[i]
                pxr_input = self.fc(pxr_input, pxr_input_size, layer_size, i, is_train, is_cpu_ps)
                pxr_input_size = layer_size
        pxr_prob = tf.nn.softmax(pxr_input, name="pxr_softmax")
        pxr_cross_entropy = tf.divide(
            tf.reduce_sum(sample_weight * tf.nn.sparse_softmax_cross_entropy_with_logits(labels=label_pxr,
                                                                                         logits=pxr_input)), n_samples,
            name='pxr_xentropy')

        # ped_head
        for i in range(self._config.klearn_conf.share_num, len(self._dnn_net_size)):
            with tf.variable_scope("ped_layer_{}".format(i)):
                layer_size = self._dnn_net_size[i]
                ped_input = self.fc(ped_input, ped_input_size, layer_size, i, is_train, is_cpu_ps)
                ped_input_size = layer_size
        ped_prob = tf.nn.softmax(ped_input, name="ped_softmax")
        ped_cross_entropy = tf.divide(
            tf.reduce_sum(sample_weight * tf.nn.sparse_softmax_cross_entropy_with_logits(labels=label_playend,
                                                                                         logits=ped_input)), n_samples,
            name='ped_xentropy')

        # ctr head
        for i in range(self._config.klearn_conf.share_num, len(self._dnn_net_size)):
            with tf.variable_scope("ctr_upper_layer_{}".format(i)):
                layer_size = self._dnn_net_size[i]
                other_input = self.fc(
                    other_input, other_input_size, layer_size, i, is_train, is_cpu_ps)
                other_input_size = layer_size
        other_prob = tf.nn.softmax(other_input, name="ctr_softmax")
        other_cross_entropy = tf.divide(
            tf.reduce_sum(sample_weight * tf.nn.sparse_softmax_cross_entropy_with_logits(labels=label_ctr,
                                                                                         logits=other_input)),
            n_samples, name='ctr_xentropy')
        
        # purchase head
        for i in range(self._config.klearn_conf.share_num, len(self._dnn_net_size)):
            with tf.variable_scope("purchase_layer_{}".format(i)):
                layer_size = self._dnn_net_size[i]
                purchase_input = self.fc(purchase_input, purchase_input_size, layer_size, i, is_train, is_cpu_ps)
                purchase_input_size = layer_size
        purchase_prob = tf.nn.softmax(purchase_input, name="purchase_softmax")
        purchase_cross_entropy = tf.divide(
            tf.reduce_sum(sample_weight * tf.nn.sparse_softmax_cross_entropy_with_logits(labels=label_approximate_purchase,
                                                                                         logits=purchase_input)),
            n_samples, name='purchase_xentropy')
  
        # leave time head (if leave time greater 1s)
        for i in range(self._config.klearn_conf.share_num, len(self._dnn_net_size)):
            with tf.variable_scope("leavetime_layer_{}".format(i)):
                layer_size = self._dnn_net_size[i]
                leave_greater_one_input = self.fc(leave_greater_one_input, leave_greater_one_input_size, layer_size, i, is_train, is_cpu_ps)
                leave_greater_one_input_size = layer_size
        leavetime_greater_one_prob = tf.nn.softmax(leave_greater_one_input, name="leavetime_softmax")
        leavetime_greater_one_cross_entropy = tf.divide(
            tf.reduce_sum(sample_weight * tf.nn.sparse_softmax_cross_entropy_with_logits(labels=label_leave_time_greater_one,
                                                                                         logits=leave_greater_one_input)),
            n_samples, name='leavetime_xentropy')
        
        # if_leave head
        saber_input_h_first = []
        for i in range(self._config.klearn_conf.share_num, len(self._dnn_net_size)):
            with tf.variable_scope("if_leave_layer_{}".format(i)):
                layer_size = self._dnn_net_size[i]
                if_leave_input = self.fc(if_leave_input, if_leave_input_size, layer_size, i, is_train, is_cpu_ps)
                if_leave_input_size = layer_size
                if i == len(self._dnn_net_size)-2:
                    saber_input_h_first.append(if_leave_input)
        if_leave_prob = tf.nn.softmax(if_leave_input, name="if_leave_softmax")
        if_leave_cross_entropy = tf.divide(
            tf.reduce_sum(sample_weight * tf.nn.sparse_softmax_cross_entropy_with_logits(labels=label_if_leave,
                                                                                         logits=if_leave_input)),
            n_samples, name='if_leave_xentropy')
        if_leave_prob = tf.clip_by_value(if_leave_prob[:,1], tf.constant(0.0), tf.constant(1.0))

        # leave time head
        for i in range(self._config.klearn_conf.share_num, len(self._dnn_net_size)-1):
            with tf.variable_scope("leave_time_seq_layer_{}".format(i)):
                layer_size = self._dnn_net_size[i]
                leave_input = self.fc(leave_input, leave_input_size, layer_size, i, is_train, is_cpu_ps)
                leave_input_size = layer_size
        with tf.variable_scope("leave_time_esmm"):
            leave_seq_input = self.fc1(leave_input, leave_input_size, self.label_nums, True)
            # tf.add_to_collection('check_ts_list', leave_seq_input)
            # logger.info("leave_seq_input weight: ", leave_seq_input.get_shape())
            leave_seq_prob = tf.clip_by_value(tf.nn.sigmoid(leave_seq_input, name='leave_seq_prob'), 0.0, 1.0)
            # logger.info("!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!seq shape is: {}".format(leave_seq_prob.get_shape()))
            
            '''esmm: 概率连乘'''  
            leave_seq_esmm_list = tf.multiply(tf.slice(leave_seq_prob, [0, 0], [-1, 1]), tf.stop_gradient(tf.reshape(if_leave_prob, [-1, 1])))
            leave_seq_esmm_list = tf.slice(leave_seq_prob, [0, 0], [-1, 1])
            # logger.info("!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!seq shape is: {}".format(leave_seq_esmm_list.get_shape()))
            for i in range(1, self.label_nums):
                with tf.variable_scope("esmm_{}".format(i)):
                    leave_ctcvr = tf.multiply(
                        tf.stop_gradient(tf.slice(leave_seq_prob, [0, i - 1], [-1, 1])),
                        tf.slice(leave_seq_prob, [0, i], [-1, 1]))
                    leave_seq_esmm_list = tf.concat([leave_seq_esmm_list, tf.reshape(leave_ctcvr, [-1, 1])], axis=1)

        leave_seq_esmm_list = tf.clip_by_value(leave_seq_esmm_list, tf.constant(0.0), tf.constant(1.0), name="leave_seq_esmm_list")
        leave_cross_entropy = tf.losses.log_loss(label_leave_time_multitask, leave_seq_esmm_list)
        cost_w = get_cost_sensitive_weight(label_leave_time_idx, self.label_nums)
        leave_cross_entropy = tf.reduce_mean(cost_w * leave_cross_entropy)

        # ctcvr head (主塔)
        ctcvr_input = input
        ctcvr_input_size = input_size
        for i in range(self._config.klearn_conf.share_num, len(self._dnn_net_size)):
            with tf.variable_scope("upper_layer_{}".format(i)):
                layer_size = self._dnn_net_size[i]
                ctcvr_input = self.fc(ctcvr_input, ctcvr_input_size, layer_size, i, is_train, is_cpu_ps)
                ctcvr_input_size = layer_size
                if i == len(self._dnn_net_size)-2:
                    ctcvr_input = tf.concat([ctcvr_input, tf.stop_gradient(leave_seq_esmm_list)], axis=1)
                    ctcvr_input_size = ctcvr_input.get_shape().as_list()[1]

        # 主塔ctcvr logits
        ctcvr_main_logits = ctcvr_input
        logger.info("ctcvr_main_logits shape: {}".format(ctcvr_main_logits.get_shape()))

        # bias网络 (搜索特征增益建模)
        # 提取搜索特征 (35个特征 * 16维 = 560维)
        search_features = self.extract_search_features(sparse_input, add_sea_input, add_sea_v2_input, add_sea_v3_input)

        # bias网络结构: 560 -> 128 -> 2
        bias_net_size = [128, 2]
        bias_input = search_features
        bias_input_size = search_features.get_shape().as_list()[1]  # 560

        for i in range(len(bias_net_size)):
            with tf.variable_scope("bias_layer_{}".format(i)):
                layer_size = bias_net_size[i]
                is_output_layer = (i == len(bias_net_size) - 1)
                bias_input = self.fc(bias_input, bias_input_size, layer_size, i, is_train, is_cpu_ps)
                bias_input_size = layer_size
                logger.info("bias_layer_{} output shape: {}".format(i, bias_input.get_shape()))

        # 使用Sigmoid限制bias_logits范围到(0,2)，避免数值不稳定
        # 0-1: 抑制主塔预测, 1-2: 增强主塔预测, 1: 中性
        bias_logits = 2.0 * tf.nn.sigmoid(bias_input)
        logger.info("bias_logits shape: {}".format(bias_logits.get_shape()))

        # TensorBoard 打印 logits 分布
        tf.summary.histogram("ctcvr_main_logits", ctcvr_main_logits)
        tf.summary.histogram("bias_logits", bias_logits)
        tf.summary.scalar("ctcvr_main_logits_mean", tf.reduce_mean(ctcvr_main_logits))
        tf.summary.scalar("bias_logits_mean", tf.reduce_mean(bias_logits))
        tf.summary.scalar("ctcvr_main_logits_std", tf.sqrt(tf.reduce_mean(tf.square(ctcvr_main_logits - tf.reduce_mean(ctcvr_main_logits)))))
        tf.summary.scalar("bias_logits_std", tf.sqrt(tf.reduce_mean(tf.square(bias_logits - tf.reduce_mean(bias_logits)))))

        # 将bias logits与主塔ctcvr logits相加
        # final_logits = ctcvr_main_logits + bias_logits

        final_logits = tf.multiply(ctcvr_main_logits, bias_logits)

        logger.info("final_logits shape: {}".format(final_logits.get_shape()))

        # TensorBoard 打印最终 logits 分布
        tf.summary.histogram("final_logits", final_logits)
        tf.summary.scalar("final_logits_mean", tf.reduce_mean(final_logits))

        prob = tf.nn.softmax(final_logits, name="softmax")
        cross_entropy = tf.divide(
            tf.reduce_sum(sample_weight * tf.nn.sparse_softmax_cross_entropy_with_logits(labels=label_ctcvr,
            logits=final_logits)), n_samples, name='xentropy')

        if self.trainable_task_weight:
            logger.info("use trainable_task_weight")
            w_ctcvr = data_ops.variable_on_cpu('w_ctcvr', [1],
                                               initializer=tf.zeros_initializer,
                                               is_cpu_ps=is_cpu_ps)
            w_ctr = data_ops.variable_on_cpu('w_ctr', [1],
                                             initializer=tf.zeros_initializer,
                                             is_cpu_ps=is_cpu_ps)
            w_pxr = data_ops.variable_on_cpu('w_pxr', [1],
                                             initializer=tf.zeros_initializer,
                                             is_cpu_ps=is_cpu_ps)
            w_ped = data_ops.variable_on_cpu('w_ped', [1],
                                             initializer=tf.zeros_initializer,
                                             is_cpu_ps=is_cpu_ps)
            # search purchase submit adlike leavetime
            w_purchase = data_ops.variable_on_cpu('w_purchase', [1],
                                             initializer=tf.zeros_initializer,
                                             is_cpu_ps=is_cpu_ps)
            w_leavetime_greater_one = data_ops.variable_on_cpu('w_leavetime', [1],
                                             initializer=tf.zeros_initializer,
                                             is_cpu_ps=is_cpu_ps) 
            w_leavetime = data_ops.variable_on_cpu('w_leave_time', [1],
                                             initializer=tf.zeros_initializer,
                                             is_cpu_ps=is_cpu_ps)  
            w_if_leave = data_ops.variable_on_cpu('w_if_leave', [1],
                                             initializer=tf.zeros_initializer,
                                             is_cpu_ps=is_cpu_ps)                                                                                                  
            train_loss = tf.add(
                cross_entropy * tf.exp(-w_ctcvr) + 0.5 * w_ctcvr +
                pxr_cross_entropy * tf.exp(-w_pxr) + 0.5 * w_pxr + 
                other_cross_entropy * tf.exp(-w_ctr) + 0.5 * w_ctr,
                ped_cross_entropy * tf.exp(-w_ped) + 0.5 * w_ped +
                purchase_cross_entropy * tf.exp(-w_purchase) + 0.5 * w_purchase + 
                leavetime_greater_one_cross_entropy * tf.exp(-w_leavetime_greater_one) + 0.5 * w_leavetime_greater_one +
                leave_cross_entropy * tf.exp(-w_leavetime) + 0.5 * w_leavetime +
                if_leave_cross_entropy * tf.exp(-w_if_leave) + 0.5 * w_if_leave,
                name='loss_weighted_sum')
        else:
            logger.info("use not trainable_task_weight")
            logger.info('weight_ctcvr:{}, weight_ctr:{}'.format(self.weight_ctcvr, self.weight_ctr))
            train_loss = tf.add(self.weight_ctcvr * cross_entropy, self.weight_ctr * other_cross_entropy,
                                name='loss_weighted_sum')
        self.prob = tf.clip_by_value(prob[:,1], tf.constant(0.0), tf.constant(1.0))        
        self.label = label_ctcvr

        pxr_prob = tf.clip_by_value(pxr_prob[:,1], tf.constant(0.0), tf.constant(1.0))
        ped_prob = tf.clip_by_value(ped_prob[:,1], tf.constant(0.0), tf.constant(1.0))
        other_prob = tf.clip_by_value(other_prob[:,1], tf.constant(0.0), tf.constant(1.0))

        # search purchase fill adlike leavetime
        purchase_prob = tf.clip_by_value(purchase_prob[:,1], tf.constant(0.0), tf.constant(1.0))
        leavetime_greater_one_prob = tf.clip_by_value(leavetime_greater_one_prob[:,1], tf.constant(0.0), tf.constant(1.0))

        with tf.device('/cpu:0'):
            _, self.auc, reset_auc_eval = auc_eval(self.label, self.prob)
        self.eval_targets = [
            ("CVR_AUC", self.prob, tf.cast(self.label, tf.float32), tf.cast(tf.ones_like(self.prob), tf.float32), "auc"),
            ("PXR_AUC", pxr_prob, tf.cast(label_pxr, tf.float32), tf.cast(tf.ones_like(pxr_prob), tf.float32), "auc"),
            ("PED_AUC", ped_prob, tf.cast(label_playend, tf.float32), tf.cast(tf.ones_like(ped_prob), tf.float32), "auc"),
            ("CTR_AUC", other_prob, tf.cast(label_ctr, tf.float32), tf.cast(tf.ones_like(other_prob), tf.float32), "auc"),
            ("PUR_AUC", purchase_prob, tf.cast(label_approximate_purchase, tf.float32), tf.cast(tf.ones_like(purchase_prob), tf.float32), "auc"),
            ("lET_1S_AUC", leavetime_greater_one_prob, tf.cast(label_leave_time_greater_one, tf.float32), tf.cast(tf.ones_like(leavetime_greater_one_prob), tf.float32), "auc"),
            ("IF_LET_AUC", if_leave_prob, tf.cast(label_if_leave, tf.float32), tf.cast(tf.ones_like(if_leave_prob), tf.float32), "auc")
        ] 
        leave_time_labels = tf.cast(label_leave_time_multitask[:, 1], tf.float32)
        leave_time_sample_weight = tf.ones(tf.shape(leave_time_labels), dtype=tf.float32)
        # for i in range(1, label_leave_time_multitask.shape[1]-1):
        #     # 第 i 个桶的AUC
        #     labels = tf.cast(label_leave_time_multitask[:, i], tf.float32)
        #     probs = leave_seq_esmm_list[:, i]
        #     self.eval_targets.append(("LET_BUCKET_AUC_" + str(i), probs, labels, leave_time_sample_weight, "auc"))

        return train_loss
