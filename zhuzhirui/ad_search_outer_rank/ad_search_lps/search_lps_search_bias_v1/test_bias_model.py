#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试bias建模的搜索特征提取功能
"""

def test_search_feature_extraction():
    """测试搜索特征提取的逻辑"""
    
    # 模拟配置
    class MockConfig:
        def __init__(self):
            self.embedding_size = 16
    
    class MockKlearnConf:
        def __init__(self):
            self.embedding_size = 16
    
    class MockModel:
        def __init__(self):
            self._config = MockConfig()
            self._config.klearn_conf = MockKlearnConf()
    
    model = MockModel()
    
    # 计算搜索特征分布
    print("=== 搜索特征分布分析 ===")
    
    # sparse embedding (slots 1-210) 中的搜索特征
    sparse_search_fields = [94, 96, 109, 115, 116, 117, 118, 119, 127]  # 用户搜索特征
    sparse_search_fields.extend([199, 200, 201, 202, 203, 204, 205])    # 照片搜索特征
    print(f"sparse embedding 中的搜索特征: {len(sparse_search_fields)}个")
    print(f"  用户搜索特征: {[94, 96, 109, 115, 116, 117, 118, 119, 127]}")
    print(f"  照片搜索特征: {[199, 200, 201, 202, 203, 204, 205]}")
    
    # add_sea embedding (slots 211-224) 中的搜索特征
    add_sea_search_fields = [213]
    print(f"add_sea embedding 中的搜索特征: {len(add_sea_search_fields)}个")
    print(f"  组合搜索特征: {add_sea_search_fields}")
    
    # add_sea_v2 embedding (slots 225-240) 中的搜索特征
    add_sea_v2_search_fields = [235, 236, 237, 238, 239, 240]
    print(f"add_sea_v2 embedding 中的搜索特征: {len(add_sea_v2_search_fields)}个")
    print(f"  组合搜索特征: {add_sea_v2_search_fields}")
    
    # add_sea_v3 embedding (slots 241-259) 中的搜索特征
    add_sea_v3_search_fields = [241, 242, 243, 244, 245, 246, 247, 248, 251, 254, 255, 256]
    print(f"add_sea_v3 embedding 中的搜索特征: {len(add_sea_v3_search_fields)}个")
    print(f"  组合搜索特征: {add_sea_v3_search_fields}")
    
    # 总计
    total_features = len(sparse_search_fields) + len(add_sea_search_fields) + len(add_sea_v2_search_fields) + len(add_sea_v3_search_fields)
    total_dim = total_features * model._config.klearn_conf.embedding_size
    
    print(f"\n=== 总计 ===")
    print(f"总搜索特征数: {total_features}个")
    print(f"总维度: {total_dim}维 ({total_features} * {model._config.klearn_conf.embedding_size})")
    
    print(f"\n=== bias网络结构 ===")
    print(f"输入层: {total_dim}维")
    print(f"第一层: {total_dim} -> 518")
    print(f"第二层: 518 -> 128")
    print(f"输出层: 128 -> 2")
    
    return total_features, total_dim

def print_search_feature_mapping():
    """打印搜索特征的详细映射"""
    print("\n=== 搜索特征详细映射 ===")
    
    search_features = [
        # sparse embedding 中的搜索特征
        (94, "ExtractSearchQuerySource", "用户", "sparse"),
        (96, "ExtractSearchReferPhotoId", "用户", "sparse"),
        (109, "ExtractSearchQueryCategoryCalss3", "用户", "sparse"),
        (115, "ExtractSearchQueryCategoryCalss2", "用户", "sparse"),
        (116, "ExtractQuery", "用户", "sparse"),
        (117, "ExtractQuerytoken", "用户", "sparse"),
        (118, "ExtractSearchFromPage", "用户", "sparse"),
        (119, "ExtractSearchPosId", "用户", "sparse"),
        (127, "ExtractSearchEnterSource", "用户", "sparse"),
        (199, "ExtractSearchRecallMatchtype", "照片", "sparse"),
        (200, "ExtractSearchRecallRelevance", "照片", "sparse"),
        (201, "ExtractSearchRecallStrategy", "照片", "sparse"),
        (202, "ExtractSearchRecallStrategyType", "照片", "sparse"),
        (203, "ExtractSearchRewriteQuery", "照片", "sparse"),
        (204, "ExtractSearchQrScore", "照片", "sparse"),
        (205, "ExtractSearchExtendType", "照片", "sparse"),
        
        # add_sea embedding 中的搜索特征
        (213, "ExtractSearchKboxType", "组合", "add_sea"),
        
        # add_sea_v2 embedding 中的搜索特征
        (235, "ExtractSearchPhotoPname", "组合", "add_sea_v2"),
        (236, "ExtractSearchPhotoPname2", "组合", "add_sea_v2"),
        (237, "ExtractSearchPhotoAsr", "组合", "add_sea_v2"),
        (238, "ExtractSearchPhotoAsr2", "组合", "add_sea_v2"),
        (239, "ExtractSearchPhotoCname", "组合", "add_sea_v2"),
        (240, "ExtractSearchPhotoCname2", "组合", "add_sea_v2"),
        
        # add_sea_v3 embedding 中的搜索特征
        (241, "ExtractSearchPhotoDescription", "组合", "add_sea_v3"),
        (242, "ExtractSearchPhotoDescription2", "组合", "add_sea_v3"),
        (243, "ExtractSearchPhotoOcr", "组合", "add_sea_v3"),
        (244, "ExtractSearchPhotoOcr2", "组合", "add_sea_v3"),
        (245, "ExtractSearchPhotoOcrTitle", "组合", "add_sea_v3"),
        (246, "ExtractSearchPhotoOcrTitle2", "组合", "add_sea_v3"),
        (247, "ExtractSearchPhotoSlogan", "组合", "add_sea_v3"),
        (248, "ExtractSearchPhotoSlogan2", "组合", "add_sea_v3"),
        (251, "ExtractSearchBidword", "组合", "add_sea_v3"),
        (254, "ExtractSearchParserTextTokenV1", "组合", "add_sea_v3"),
        (255, "ExtractSearchParserTextV1", "组合", "add_sea_v3"),
        (256, "ExtractSearchQueryCombineMatchNum", "组合", "add_sea_v3"),
    ]
    
    for field_id, name, category, embedding in search_features:
        print(f"  field {field_id:3d}: {name:40s} ({category:4s}) -> {embedding}")
    
    print(f"\n总计: {len(search_features)}个搜索特征")

if __name__ == "__main__":
    print("🐾 测试bias建模的搜索特征提取")
    
    total_features, total_dim = test_search_feature_extraction()
    print_search_feature_mapping()
    
    print(f"\n✅ 搜索特征提取逻辑验证完成!")
    print(f"   - 总特征数: {total_features}")
    print(f"   - 总维度: {total_dim}")
    print(f"   - bias网络: {total_dim} -> 518 -> 128 -> 2")
