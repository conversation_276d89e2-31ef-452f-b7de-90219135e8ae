remap_slot = 241, dim = 16, map_slot = 241, size = 0, slot = 0, name = ExtractSignUserIp, category = ad, field = 0, feature_size = 100001
remap_slot = 1, dim = 16, map_slot = 1, size = 0, slot = 0, name = ExtractUserAdLpsExtendWithTime, category = ad, field = 1, feature_size = 100001
remap_slot = 2, dim = 16, map_slot = 2, size = 0, slot = 0, name = ExtractUserAdItemClickNoLps, category = ad, field = 2, feature_size = 100001
remap_slot = 3, dim = 16, map_slot = 3, size = 0, slot = 0, name = ExtractUserViewLikePhotoLabel, category = ad, field = 3, feature_size = 100001
remap_slot = 4, dim = 16, map_slot = 4, size = 0, slot = 0, name = ExtractUserAdPlayendExtend, category = ad, field = 4, feature_size = 100001
remap_slot = 5, dim = 16, map_slot = 5, size = 0, slot = 0, name = ExtractUserInstalledApp, category = ad, field = 5, feature_size = 100001
remap_slot = 6, dim = 16, map_slot = 6, size = 0, slot = 0, name = ExtractUserClickLargeNew, category = ad, field = 6, feature_size = 10000000
remap_slot = 7, dim = 16, map_slot = 7, size = 0, slot = 0, name = ExtractUserAdItemClickExtendWithTime, category = ad, field = 7, feature_size = 100001
remap_slot = 8, dim = 16, map_slot = 8, size = 0, slot = 0, name = ExtractUserDeviceInfoAdUser, category = ad, field = 8, feature_size = 100001
remap_slot = 9, dim = 16, map_slot = 9, size = 0, slot = 0, name = ExtractUserDatetime, category = ad, field = 9, feature_size = 100000
remap_slot = 10, dim = 16, map_slot = 10, size = 0, slot = 0, name = ExtractUserClickAuthorNew, category = ad, field = 10, feature_size = 100001
remap_slot = 11, dim = 16, map_slot = 11, size = 0, slot = 0, name = ExtractUserAdItemClickExtendEcomShortWithTime, category = ad, field = 11, feature_size = 100001
remap_slot = 12, dim = 16, map_slot = 12, size = 0, slot = 0, name = ExtractUserAdClickNoLps, category = ad, field = 12, feature_size = 100001
remap_slot = 13, dim = 16, map_slot = 13, size = 0, slot = 0, name = ExtractUserAdPlay5sExtend, category = ad, field = 13, feature_size = 100001
remap_slot = 14, dim = 16, map_slot = 14, size = 0, slot = 0, name = ExtractUserModelName, category = ad, field = 14, feature_size = 100001
remap_slot = 15, dim = 16, map_slot = 15, size = 0, slot = 0, name = ExtractUserEshopClickItemCate3Id, category = ad, field = 15, feature_size = 100001
remap_slot = 16, dim = 16, map_slot = 16, size = 0, slot = 0, name = ExtractUserAdPlayendExtendShort, category = ad, field = 16, feature_size = 100001
remap_slot = 17, dim = 16, map_slot = 17, size = 0, slot = 0, name = ExtractUserPhotoBuyItemSellerIdList, category = ad, field = 17, feature_size = 100001
remap_slot = 18, dim = 16, map_slot = 18, size = 0, slot = 0, name = ExtractUserAdPlay3sExtendEcomWithTime, category = ad, field = 18, feature_size = 1000001
remap_slot = 19, dim = 16, map_slot = 19, size = 0, slot = 0, name = ExtractUserAdPlayedFix5sNoItemClick, category = ad, field = 19, feature_size = 100001
remap_slot = 20, dim = 16, map_slot = 20, size = 0, slot = 0, name = ExtractLiveUserBehavStatOnline, category = ad, field = 20, feature_size = 1000000
remap_slot = 21, dim = 16, map_slot = 21, size = 0, slot = 0, name = ExtractUserAdClickExtendEcom, category = ad, field = 21, feature_size = 1000001
remap_slot = 22, dim = 16, map_slot = 22, size = 0, slot = 0, name = ExtractUserMerchantClickCartSellerIdList, category = ad, field = 22, feature_size = 100001
remap_slot = 23, dim = 16, map_slot = 23, size = 0, slot = 0, name = ExtractUserAdItemClickExtendEcomWithTime, category = ad, field = 23, feature_size = 1000001
remap_slot = 24, dim = 16, map_slot = 24, size = 0, slot = 0, name = ExtractUserEshopBuyItemCate2Id, category = ad, field = 24, feature_size = 100001
remap_slot = 25, dim = 16, map_slot = 25, size = 0, slot = 0, name = ExtractUserAdItemClickExtendShort, category = ad, field = 25, feature_size = 100001
remap_slot = 26, dim = 16, map_slot = 26, size = 0, slot = 0, name = ExtractUserAppList, category = ad, field = 26, feature_size = 100001
remap_slot = 27, dim = 16, map_slot = 27, size = 0, slot = 0, name = ExtractUserAdPlay3sExtend, category = ad, field = 27, feature_size = 100001
remap_slot = 28, dim = 16, map_slot = 28, size = 0, slot = 0, name = ExtractUserFollowNewhash, category = ad, field = 28, feature_size = 100001
remap_slot = 29, dim = 16, map_slot = 29, size = 0, slot = 0, name = ExtractUserAdLpsExtendEcomWithTime, category = ad, field = 29, feature_size = 1000001
remap_slot = 30, dim = 16, map_slot = 30, size = 0, slot = 0, name = ExtractUserItemClickActionNebula, category = ad, field = 30, feature_size = 1000001
remap_slot = 31, dim = 16, map_slot = 31, size = 0, slot = 0, name = ExtractUserAdClickExtendShort, category = ad, field = 31, feature_size = 100001
remap_slot = 32, dim = 16, map_slot = 32, size = 0, slot = 0, name = ExtractUserAdPlayed5sNoItemClick, category = ad, field = 32, feature_size = 100001
remap_slot = 33, dim = 16, map_slot = 33, size = 0, slot = 0, name = ExtractUserMerchantBuyItemIdList, category = ad, field = 33, feature_size = 100001
remap_slot = 34, dim = 16, map_slot = 34, size = 0, slot = 0, name = ExtractUserFollow, category = ad, field = 34, feature_size = 100001
remap_slot = 35, dim = 16, map_slot = 35, size = 0, slot = 0, name = ExtractUserAdItemImpression, category = ad, field = 35, feature_size = 100001
remap_slot = 36, dim = 16, map_slot = 36, size = 0, slot = 0, name = ExtractUserEshopBuyItemCate1Id, category = ad, field = 36, feature_size = 100001
remap_slot = 37, dim = 16, map_slot = 37, size = 0, slot = 0, name = ExtractUserAdLpsExtend, category = ad, field = 37, feature_size = 100001
remap_slot = 38, dim = 16, map_slot = 38, size = 0, slot = 0, name = ExtractUserAdImpressionRealtime, category = ad, field = 38, feature_size = 100001
remap_slot = 39, dim = 16, map_slot = 39, size = 0, slot = 0, name = ExtractUserClickLiveAuthorListNear, category = ad, field = 39, feature_size = 3000000
remap_slot = 40, dim = 16, map_slot = 40, size = 0, slot = 0, name = ExtractUserContextInfo, category = ad, field = 40, feature_size = 100001
remap_slot = 41, dim = 16, map_slot = 41, size = 0, slot = 0, name = ExtractUserTop3ItemImpressionRealtime, category = ad, field = 41, feature_size = 100001
remap_slot = 42, dim = 16, map_slot = 42, size = 0, slot = 0, name = ExtractUserEshopClickItemCate1Id, category = ad, field = 42, feature_size = 100001
remap_slot = 43, dim = 16, map_slot = 43, size = 0, slot = 0, name = ExtractUserLoc, category = ad, field = 43, feature_size = 100001
remap_slot = 44, dim = 16, map_slot = 44, size = 0, slot = 0, name = ExtractUserPlay3sActionNebula, category = ad, field = 44, feature_size = 1000001
remap_slot = 45, dim = 16, map_slot = 45, size = 0, slot = 0, name = ExtractUserRecoNebulaClick, category = ad, field = 45, feature_size = 1000001
remap_slot = 46, dim = 16, map_slot = 46, size = 0, slot = 0, name = ExtractUserRecoNebulaLike, category = ad, field = 46, feature_size = 1000001
remap_slot = 47, dim = 16, map_slot = 47, size = 0, slot = 0, name = ExtractLiveUserToDetailMerchantItemList, category = ad, field = 47, feature_size = 1000000
remap_slot = 48, dim = 16, map_slot = 48, size = 0, slot = 0, name = ExtractUserClickActionNebula, category = ad, field = 48, feature_size = 100001
remap_slot = 49, dim = 16, map_slot = 49, size = 0, slot = 0, name = ExtractUserAdItemClickExtend, category = ad, field = 49, feature_size = 100001
remap_slot = 50, dim = 16, map_slot = 50, size = 0, slot = 0, name = ExtractUserEshopClickItemSellerId, category = ad, field = 50, feature_size = 100001
remap_slot = 51, dim = 16, map_slot = 51, size = 0, slot = 0, name = ExtractUserEshopBuyItemSellerId, category = ad, field = 51, feature_size = 100001
remap_slot = 52, dim = 16, map_slot = 52, size = 0, slot = 0, name = ExtractUserMerchantClickCardSellerIdList, category = ad, field = 52, feature_size = 100001
remap_slot = 53, dim = 16, map_slot = 53, size = 0, slot = 0, name = ExtractUserAdImpressionExtendShort, category = ad, field = 53, feature_size = 100001
remap_slot = 54, dim = 16, map_slot = 54, size = 0, slot = 0, name = ExtractUserNebulaAdPlayendExtend, category = ad, field = 54, feature_size = 100001
remap_slot = 55, dim = 16, map_slot = 55, size = 0, slot = 0, name = ExtractUserItemClickIndustry, category = ad, field = 55, feature_size = 100001
remap_slot = 56, dim = 16, map_slot = 56, size = 0, slot = 0, name = ExtractUserAdItemClick, category = ad, field = 56, feature_size = 100001
remap_slot = 57, dim = 16, map_slot = 57, size = 0, slot = 0, name = ExtractUserMerchantClickItemIdList, category = ad, field = 57, feature_size = 100001
remap_slot = 58, dim = 16, map_slot = 58, size = 0, slot = 0, name = ExtractUserCountFeature, category = ad, field = 58, feature_size = 100001
remap_slot = 59, dim = 16, map_slot = 59, size = 0, slot = 0, name = ExtractUserAdClick2NoConvExtend, category = ad, field = 59, feature_size = 100001
remap_slot = 60, dim = 16, map_slot = 60, size = 0, slot = 0, name = ExtractUserAdPlay5sExtendShort, category = ad, field = 60, feature_size = 100001
remap_slot = 61, dim = 16, map_slot = 61, size = 0, slot = 0, name = ExtractUserAdClick1NoClick2Extend, category = ad, field = 61, feature_size = 100001
remap_slot = 62, dim = 16, map_slot = 62, size = 0, slot = 0, name = ExtractUserAdPlayendExtendSimpleIndustry, category = ad, field = 62, feature_size = 100001
remap_slot = 63, dim = 16, map_slot = 63, size = 0, slot = 0, name = ExtractLiveUserBehavOnline, category = ad, field = 63, feature_size = 1000001
remap_slot = 64, dim = 16, map_slot = 64, size = 0, slot = 0, name = ExtractUserAdItemImpressionNewhash, category = ad, field = 64, feature_size = 100001
remap_slot = 65, dim = 16, map_slot = 65, size = 0, slot = 0, name = ExtractUserAdLpsExtendShort, category = ad, field = 65, feature_size = 100001
remap_slot = 66, dim = 16, map_slot = 66, size = 0, slot = 0, name = ExtractUserAdPlayendExtendSimpleProduct, category = ad, field = 66, feature_size = 100001
remap_slot = 67, dim = 16, map_slot = 67, size = 0, slot = 0, name = ExtractUserAdPlay5sExtendSimpleProduct, category = ad, field = 67, feature_size = 100001
remap_slot = 68, dim = 16, map_slot = 68, size = 0, slot = 0, name = ExtractUserAdDownloadNew, category = ad, field = 68, feature_size = 100001
remap_slot = 69, dim = 16, map_slot = 69, size = 0, slot = 0, name = ExtractUserAdPlay3sExtendShort, category = ad, field = 69, feature_size = 100001
remap_slot = 70, dim = 16, map_slot = 70, size = 0, slot = 0, name = ExtractUserAdLike, category = ad, field = 70, feature_size = 100001
remap_slot = 71, dim = 16, map_slot = 71, size = 0, slot = 0, name = ExtractUserAdPlay5sExtendEdu, category = ad, field = 71, feature_size = 100001
remap_slot = 72, dim = 16, map_slot = 72, size = 0, slot = 0, name = ExtractUserFollowPhoto, category = ad, field = 72, feature_size = 100001
remap_slot = 73, dim = 16, map_slot = 73, size = 0, slot = 0, name = ExtractUserAdLpsExtendSMBWithTime, category = ad, field = 73, feature_size = 1000001
remap_slot = 74, dim = 16, map_slot = 74, size = 0, slot = 0, name = ExtractUserAdPlay5sExtendSimpleIndustry, category = ad, field = 74, feature_size = 100001
remap_slot = 75, dim = 16, map_slot = 75, size = 0, slot = 0, name = ExtractUserAdPurchaseExtendShort, category = ad, field = 75, feature_size = 100001
remap_slot = 76, dim = 16, map_slot = 76, size = 0, slot = 0, name = ExtractUserAdDeviceInfo, category = ad, field = 76, feature_size = 100001
remap_slot = 77, dim = 16, map_slot = 77, size = 0, slot = 0, name = ExtractUserPlayendActionNebula, category = ad, field = 77, feature_size = 100001
remap_slot = 78, dim = 16, map_slot = 78, size = 0, slot = 0, name = ExtractUserAdImpressionExtendEcom, category = ad, field = 78, feature_size = 1000001
remap_slot = 79, dim = 16, map_slot = 79, size = 0, slot = 0, name = ExtractUserAdClickExtendEdu, category = ad, field = 79, feature_size = 100001
remap_slot = 80, dim = 16, map_slot = 80, size = 0, slot = 0, name = ExtractUserNegativeNew, category = ad, field = 80, feature_size = 100001
remap_slot = 81, dim = 16, map_slot = 81, size = 0, slot = 0, name = ExtractUserClickNewhash, category = ad, field = 81, feature_size = 100001
remap_slot = 82, dim = 16, map_slot = 82, size = 0, slot = 0, name = ExtractUserAdDownload, category = ad, field = 82, feature_size = 100001
remap_slot = 83, dim = 16, map_slot = 83, size = 0, slot = 0, name = ExtractUserAdImpression, category = ad, field = 83, feature_size = 1000001
remap_slot = 84, dim = 16, map_slot = 84, size = 0, slot = 0, name = ExtractUserLiveMmuCluster2W, category = ad, field = 84, feature_size = 200000
remap_slot = 85, dim = 16, map_slot = 85, size = 0, slot = 0, name = ExtractUserAdDeviceInfoNewhash, category = ad, field = 85, feature_size = 100001
remap_slot = 86, dim = 16, map_slot = 86, size = 0, slot = 0, name = ExtractUserAdWeekStay, category = ad, field = 86, feature_size = 100001
remap_slot = 87, dim = 16, map_slot = 87, size = 0, slot = 0, name = ExtractUserInteractiveForm, category = ad, field = 87, feature_size = 11
remap_slot = 88, dim = 16, map_slot = 88, size = 0, slot = 0, name = ExtractUserClickNew, category = ad, field = 88, feature_size = 1000001
remap_slot = 89, dim = 16, map_slot = 89, size = 0, slot = 0, name = ExtractUserAdPlay5sExtendEcom, category = ad, field = 89, feature_size = 1000001
remap_slot = 90, dim = 16, map_slot = 90, size = 0, slot = 0, name = ExtractUserAdItemClickExtendEcom, category = ad, field = 90, feature_size = 1000001
remap_slot = 91, dim = 16, map_slot = 91, size = 0, slot = 0, name = ExtractUserEshopBuyItemIdList, category = ad, field = 91, feature_size = 500001
remap_slot = 92, dim = 16, map_slot = 92, size = 0, slot = 0, name = ExtractUserEshopClickItemIdList, category = ad, field = 92, feature_size = 1000001
remap_slot = 93, dim = 16, map_slot = 93, size = 0, slot = 0, name = ExtractUserEshopClickItemCate2Id, category = ad, field = 93, feature_size = 1000001
remap_slot = 94, dim = 16, map_slot = 94, size = 0, slot = 0, name = ExtractSearchQuerySource, category = ad, field = 94, feature_size = 100001
remap_slot = 95, dim = 16, map_slot = 95, size = 0, slot = 0, name = ExtractUserEcomOrderFirstCateList, category = ad, field = 95, feature_size = 1001
remap_slot = 96, dim = 16, map_slot = 96, size = 0, slot = 0, name = ExtractSearchReferPhotoId, category = ad, field = 96, feature_size = 1000001
remap_slot = 97, dim = 16, map_slot = 97, size = 0, slot = 0, name = ExtractUserAdPlay5sExtendEcomWithTime, category = ad, field = 97, feature_size = 2000001
remap_slot = 98, dim = 16, map_slot = 98, size = 0, slot = 0, name = ExtractUserAdImpressionExtendEcomWithTime, category = ad, field = 98, feature_size = 1000001
remap_slot = 99, dim = 16, map_slot = 99, size = 0, slot = 0, name = ExtractUserAdClick1NoPlay3sExtend, category = ad, field = 99, feature_size = 500001
remap_slot = 100, dim = 16, map_slot = 100, size = 0, slot = 0, name = ExtractUserAdPlayendExtendEcomWithTime, category = ad, field = 100, feature_size = 500001
remap_slot = 101, dim = 16, map_slot = 101, size = 0, slot = 0, name = ExtractUserAdOrderSubmitExtend, category = ad, field = 101, feature_size = 1000001
remap_slot = 102, dim = 16, map_slot = 102, size = 0, slot = 0, name = ExtractLiveUserMerchantListOnline, category = ad, field = 102, feature_size = 100001
remap_slot = 103, dim = 16, map_slot = 103, size = 0, slot = 0, name = ExtractUserAdClickExtendEcomFix, category = ad, field = 103, feature_size = 500001
remap_slot = 104, dim = 16, map_slot = 104, size = 0, slot = 0, name = ExtractUserPlayEndHetuTag, category = ad, field = 104, feature_size = 100001
remap_slot = 105, dim = 16, map_slot = 105, size = 0, slot = 0, name = ExtractUserEcomClick2ItemIds, category = ad, field = 105, feature_size = 100001
remap_slot = 106, dim = 16, map_slot = 106, size = 0, slot = 0, name = ExtractUserEcomClick2ProductIds, category = ad, field = 106, feature_size = 100001
remap_slot = 107, dim = 16, map_slot = 107, size = 0, slot = 0, name = ExtractUserEcomLpsItemIds, category = ad, field = 107, feature_size = 100001
remap_slot = 108, dim = 16, map_slot = 108, size = 0, slot = 0, name = ExtractUserEcomLpsCate1Ids, category = ad, field = 108, feature_size = 100001
remap_slot = 109, dim = 16, map_slot = 109, size = 0, slot = 0, name = ExtractSearchQueryCategoryCalss3, category = ad, field = 109, feature_size = 300001
remap_slot = 110, dim = 16, map_slot = 110, size = 0, slot = 0, name = ExtractUserAdPlayedendNoLpsEcom, category = ad, field = 110, feature_size = 300001
remap_slot = 111, dim = 16, map_slot = 111, size = 0, slot = 0, name = ExtractUserAdOrderPaiedExtend, category = ad, field = 111, feature_size = 300001
remap_slot = 112, dim = 16, map_slot = 112, size = 0, slot = 0, name = ExtractUserEcomClick2Ind2List90d, category = ad, field = 112, feature_size = 100001
remap_slot = 113, dim = 16, map_slot = 113, size = 0, slot = 0, name = ExtractUserEcomClick2Cate3List90d, category = ad, field = 113, feature_size = 100001
remap_slot = 114, dim = 16, map_slot = 114, size = 0, slot = 0, name = ExtractUserMerchatBuyNewCate1List15d, category = ad, field = 114, feature_size = 100001
remap_slot = 115, dim = 16, map_slot = 115, size = 0, slot = 0, name = ExtractSearchQueryCategoryCalss2, category = ad, field = 115, feature_size = 300001
remap_slot = 116, dim = 16, map_slot = 116, size = 0, slot = 0, name = ExtractQuery, category = ad, field = 116, feature_size = 1000001
remap_slot = 117, dim = 16, map_slot = 117, size = 0, slot = 0, name = ExtractQuerytoken, category = ad, field = 117, feature_size = 1000001
remap_slot = 118, dim = 16, map_slot = 118, size = 0, slot = 0, name = ExtractSearchFromPage, category = ad, field = 118, feature_size = 11
remap_slot = 119, dim = 16, map_slot = 119, size = 0, slot = 0, name = ExtractSearchPosId, category = ad, field = 119, feature_size = 51
remap_slot = 211, dim = 16, map_slot = 211, size = 0, slot = 0, name = ExtractUserId, category = ad, field = 120, feature_size = 10000001
remap_slot = 212, dim = 16, map_slot = 212, size = 0, slot = 0, name = ExtractUserBusinessInterest, category = ad, field = 121, feature_size = 100001
remap_slot = 213, dim = 16, map_slot = 213, size = 0, slot = 0, name = ExtractUserAdPlayedendNoLps, category = ad, field = 122, feature_size = 100001
remap_slot = 214, dim = 16, map_slot = 214, size = 0, slot = 0, name = ExtractUserAdPlayed5sNoLps, category = ad, field = 123, feature_size = 100001
remap_slot = 215, dim = 16, map_slot = 215, size = 0, slot = 0, name = ExtractUserEcomLpsProductIds, category = ad, field = 124, feature_size = 100001
remap_slot = 216, dim = 16, map_slot = 216, size = 0, slot = 0, name = ExtractUserEcomLpsSellerIds, category = ad, field = 125, feature_size = 100001
remap_slot = 217, dim = 16, map_slot = 217, size = 0, slot = 0, name = ExtractUserAdItemClickNoLpsEcom, category = ad, field = 126, feature_size = 100001
remap_slot = 218, dim = 16, map_slot = 218, size = 0, slot = 0, name = ExtractSearchEnterSource, category = ad, field = 127, feature_size = 101
remap_slot = 225, dim = 16, map_slot = 225, size = 0, slot = 0, name = ExtractUserRealtimeNewItemimpNoPlayed3s, category = ad, field = 128, feature_size = 1000001
remap_slot = 226, dim = 16, map_slot = 226, size = 0, slot = 0, name = ExtractUserRealtimeNewPlay3sNoPlayend, category = ad, field = 129, feature_size = 1000001
remap_slot = 227, dim = 16, map_slot = 227, size = 0, slot = 0, name = ExtractUserRealtimeNewPlay3sNoGoodsview, category = ad, field = 130, feature_size = 1000001
remap_slot = 228, dim = 16, map_slot = 228, size = 0, slot = 0, name = ExtractUserRealtimeNewPlay3sNoItemclick, category = ad, field = 131, feature_size = 1000001
remap_slot = 229, dim = 16, map_slot = 229, size = 0, slot = 0, name = ExtractUserRealtimeNewPlay3sNoOrderpay, category = ad, field = 132, feature_size = 1000001
remap_slot = 230, dim = 16, map_slot = 230, size = 0, slot = 0, name = ExtractUserRealtimeNewPlayendNoGoodsview, category = ad, field = 133, feature_size = 1000001
remap_slot = 231, dim = 16, map_slot = 231, size = 0, slot = 0, name = ExtractUserRealtimeNewPlayendNoItemclick, category = ad, field = 134, feature_size = 1000001
remap_slot = 232, dim = 16, map_slot = 232, size = 0, slot = 0, name = ExtractUserRealtimeNewPlayendNoOrderpay, category = ad, field = 135, feature_size = 1000001
remap_slot = 233, dim = 16, map_slot = 233, size = 0, slot = 0, name = ExtractUserRealtimeNewItemclickNoOrderpay, category = ad, field = 136, feature_size = 1000001
remap_slot = 234, dim = 16, map_slot = 234, size = 0, slot = 0, name = ExtractUserRealtimeActionPlayLt1sTimeStampPhoto, category = ad, field = 137, feature_size = 1000001
remap_slot = 235, dim = 16, map_slot = 235, size = 0, slot = 0, name = ExtractUserRealtimeActionPlayLt1sPhotoCreativeId, category = ad, field = 138, feature_size = 5000001
remap_slot = 236, dim = 16, map_slot = 236, size = 0, slot = 0, name = ExtractUserRealtimeActionPlayLt1sPhotoCampaignId, category = ad, field = 139, feature_size = 1000001
remap_slot = 237, dim = 16, map_slot = 237, size = 0, slot = 0, name = ExtractUserRealtimeActionPlayLt1sPhotoAuthorId, category = ad, field = 140, feature_size = 5000001
remap_slot = 238, dim = 16, map_slot = 238, size = 0, slot = 0, name = ExtractUserRealtimeActionPlayLt1sPhotoIndId, category = ad, field = 141, feature_size = 100001
remap_slot = 239, dim = 16, map_slot = 239, size = 0, slot = 0, name = ExtractUserRealtimeActionPlayLt1sPhotoPosId, category = ad, field = 142, feature_size = 10001
remap_slot = 240, dim = 16, map_slot = 240, size = 0, slot = 0, name = ExtractUserRealtimeActionPlayLt1sPhotoItemType, category = ad, field = 143, feature_size = 1001
remap_slot = 242, dim = 16, map_slot = 242, size = 0, slot = 0, name = ExtractUserAdClickExtendEcomWithTime, category = ad, field = 144, feature_size = 1000001
remap_slot = 243, dim = 16, map_slot = 243, size = 0, slot = 0, name = ExtractMmuCommerceUserCommentCat28StationeryPhotoList, category = ad, field = 145, feature_size = 1000001
remap_slot = 244, dim = 16, map_slot = 244, size = 0, slot = 0, name = ExtractUserPhotoFieldRecent200AdphotoClusteridEnd2EndTop30, category = ad, field = 146, feature_size = 100001
remap_slot = 245, dim = 16, map_slot = 245, size = 0, slot = 0, name = ExtractUAdPhotoPlayedEndPhotoIdList, category = ad, field = 147, feature_size = 100001
remap_slot = 246, dim = 16, map_slot = 246, size = 0, slot = 0, name = ExtractUserPhotoFieldRecent200AdphotoClusteridEnd2EndSetall, category = ad, field = 148, feature_size = 100001
remap_slot = 247, dim = 16, map_slot = 247, size = 0, slot = 0, name = ExtractUserEduSdpaRelatServPendList, category = ad, field = 149, feature_size = 10001
remap_slot = 248, dim = 16, map_slot = 248, size = 0, slot = 0, name = ExtractUserEduSdpaCate3ClickList, category = ad, field = 150, feature_size = 10001
remap_slot = 249, dim = 16, map_slot = 249, size = 0, slot = 0, name = ExtractUserEduSdpaRelatServClickList, category = ad, field = 151, feature_size = 10001
remap_slot = 250, dim = 16, map_slot = 250, size = 0, slot = 0, name = ExtractUserEduSdpaCate3P3sList, category = ad, field = 152, feature_size = 10001
remap_slot = 251, dim = 16, map_slot = 251, size = 0, slot = 0, name = ExtractUserEduSdpaBrandP3sList, category = ad, field = 153, feature_size = 100001
remap_slot = 252, dim = 16, map_slot = 252, size = 0, slot = 0, name = ExtractUserEduSdpaTarAudP3sList, category = ad, field = 154, feature_size = 1001
remap_slot = 253, dim = 16, map_slot = 253, size = 0, slot = 0, name = ExtractUserEduSdpaRelatServP3sList, category = ad, field = 155, feature_size = 10001
remap_slot = 254, dim = 16, map_slot = 254, size = 0, slot = 0, name = ExtractUserEduSdpaCate2PendList, category = ad, field = 156, feature_size = 1001
remap_slot = 255, dim = 16, map_slot = 255, size = 0, slot = 0, name = ExtractUserEduSdpaBrandPendList, category = ad, field = 157, feature_size = 100001
remap_slot = 256, dim = 16, map_slot = 256, size = 0, slot = 0, name = ExtractUserEduSdpaTarAudPendList, category = ad, field = 158, feature_size = 1001
remap_slot = 120, dim = 16, map_slot = 120, size = 0, slot = 0, name = ExtractPhotoAdvertiserInfo, category = ad, field = 159, feature_size = 100001
remap_slot = 121, dim = 16, map_slot = 121, size = 0, slot = 0, name = ExtractAdNewIndustry, category = ad, field = 160, feature_size = 100001
remap_slot = 122, dim = 16, map_slot = 122, size = 0, slot = 0, name = ExtractUniverseCombineSubContextProduct, category = ad, field = 161, feature_size = 100001
remap_slot = 123, dim = 16, map_slot = 123, size = 0, slot = 0, name = ExtractPhotoDupPhotoId, category = ad, field = 162, feature_size = 100001
remap_slot = 124, dim = 16, map_slot = 124, size = 0, slot = 0, name = ExtractUniverseCombineAppContextProduct, category = ad, field = 163, feature_size = 100001
remap_slot = 125, dim = 16, map_slot = 125, size = 0, slot = 0, name = ExtractUniverseCombineUidContextProduct, category = ad, field = 164, feature_size = 100001
remap_slot = 126, dim = 16, map_slot = 126, size = 0, slot = 0, name = ExtractAdCategory, category = ad, field = 165, feature_size = 100001
remap_slot = 127, dim = 16, map_slot = 127, size = 0, slot = 0, name = ExtractAdAcountId, category = ad, field = 166, feature_size = 100001
remap_slot = 128, dim = 16, map_slot = 128, size = 0, slot = 0, name = ExtractPhotoBaseUriLps, category = ad, field = 167, feature_size = 100001
remap_slot = 129, dim = 16, map_slot = 129, size = 0, slot = 0, name = ExtractPhotoOnlyAuthor, category = ad, field = 168, feature_size = 1000001
remap_slot = 130, dim = 16, map_slot = 130, size = 0, slot = 0, name = ExtractPhoto20wSpeechECKwdIdxs, category = ad, field = 169, feature_size = 1000001
remap_slot = 131, dim = 16, map_slot = 131, size = 0, slot = 0, name = ExtractPhotoBaseUri, category = ad, field = 170, feature_size = 1000001
remap_slot = 132, dim = 16, map_slot = 132, size = 0, slot = 0, name = ExtractAdUriLps, category = ad, field = 171, feature_size = 100001
remap_slot = 133, dim = 16, map_slot = 133, size = 0, slot = 0, name = ExtractAdUri, category = ad, field = 172, feature_size = 100001
remap_slot = 134, dim = 16, map_slot = 134, size = 0, slot = 0, name = ExtractUniverseCombineAccountMediaPos, category = ad, field = 173, feature_size = 1000001
remap_slot = 135, dim = 16, map_slot = 135, size = 0, slot = 0, name = ExtractPhotoBidType, category = ad, field = 174, feature_size = 100001
remap_slot = 136, dim = 16, map_slot = 136, size = 0, slot = 0, name = ExtractCreativeSupportTag, category = ad, field = 175, feature_size = 100001
remap_slot = 137, dim = 16, map_slot = 137, size = 0, slot = 0, name = ExtractAdDescriptionSeg, category = ad, field = 176, feature_size = 100001
remap_slot = 138, dim = 16, map_slot = 138, size = 0, slot = 0, name = ExtractPhotoId, category = ad, field = 177, feature_size = 100001
remap_slot = 139, dim = 16, map_slot = 139, size = 0, slot = 0, name = ExtractPhotoAppInfo, category = ad, field = 178, feature_size = 100001
remap_slot = 140, dim = 16, map_slot = 140, size = 0, slot = 0, name = ExtractAdDupCoverId, category = ad, field = 179, feature_size = 100001
remap_slot = 141, dim = 16, map_slot = 141, size = 0, slot = 0, name = ExtractPhotoTextFeatureBertCluster, category = ad, field = 180, feature_size = 100001
remap_slot = 142, dim = 16, map_slot = 142, size = 0, slot = 0, name = ExtractCrmProductType, category = ad, field = 181, feature_size = 100001
remap_slot = 143, dim = 16, map_slot = 143, size = 0, slot = 0, name = ExtractAdLpsUrlDomainNew, category = ad, field = 182, feature_size = 100001
remap_slot = 144, dim = 16, map_slot = 144, size = 0, slot = 0, name = ExtractPhotoProductShare, category = ad, field = 183, feature_size = 200001
remap_slot = 145, dim = 16, map_slot = 145, size = 0, slot = 0, name = ExtractPhotoEducationClassInfo, category = ad, field = 184, feature_size = 100001
remap_slot = 146, dim = 16, map_slot = 146, size = 0, slot = 0, name = ExtractAdIndustryGroup, category = ad, field = 185, feature_size = 1001
remap_slot = 147, dim = 16, map_slot = 147, size = 0, slot = 0, name = ExtractPhotoIdDetail, category = ad, field = 186, feature_size = 100001
remap_slot = 148, dim = 16, map_slot = 148, size = 0, slot = 0, name = ExtractPhotoAuthor, category = ad, field = 187, feature_size = 100001
remap_slot = 149, dim = 16, map_slot = 149, size = 0, slot = 0, name = ExtractPhotoCaptionSegment, category = ad, field = 188, feature_size = 100001
remap_slot = 150, dim = 16, map_slot = 150, size = 0, slot = 0, name = ExtractPhotoPlayStat, category = ad, field = 189, feature_size = 10001
remap_slot = 151, dim = 16, map_slot = 151, size = 0, slot = 0, name = ExtractPhotoEcomOrderType, category = ad, field = 190, feature_size = 101
remap_slot = 152, dim = 16, map_slot = 152, size = 0, slot = 0, name = ExtractOnlineRetailInfoSpu, category = ad, field = 191, feature_size = 100001
remap_slot = 153, dim = 16, map_slot = 153, size = 0, slot = 0, name = ExtractOnlineRetailInfoCategory, category = ad, field = 192, feature_size = 10001
remap_slot = 154, dim = 16, map_slot = 154, size = 0, slot = 0, name = ExtractOnlineRetailInfoProduct, category = ad, field = 193, feature_size = 100001
remap_slot = 155, dim = 16, map_slot = 155, size = 0, slot = 0, name = ExtractPhotoMmuHetuTag, category = ad, field = 194, feature_size = 100001
remap_slot = 156, dim = 16, map_slot = 156, size = 0, slot = 0, name = ExtractEcomProductNameKwList, category = ad, field = 195, feature_size = 100001
remap_slot = 157, dim = 16, map_slot = 157, size = 0, slot = 0, name = ExtractPhotoGlobalStat, category = ad, field = 196, feature_size = 100002
remap_slot = 158, dim = 16, map_slot = 158, size = 0, slot = 0, name = ExtractOnlineRetailPayType, category = ad, field = 197, feature_size = 101
remap_slot = 159, dim = 16, map_slot = 159, size = 0, slot = 0, name = ExtractPhotoProductName, category = ad, field = 198, feature_size = 1000001
remap_slot = 160, dim = 16, map_slot = 160, size = 0, slot = 0, name = ExtractSearchRecallMatchtype, category = ad, field = 199, feature_size = 11
remap_slot = 161, dim = 16, map_slot = 161, size = 0, slot = 0, name = ExtractSearchRecallRelevance, category = ad, field = 200, feature_size = 1001
remap_slot = 162, dim = 16, map_slot = 162, size = 0, slot = 0, name = ExtractSearchRecallStrategy, category = ad, field = 201, feature_size = 51
remap_slot = 163, dim = 16, map_slot = 163, size = 0, slot = 0, name = ExtractSearchRecallStrategyType, category = ad, field = 202, feature_size = 51
remap_slot = 164, dim = 16, map_slot = 164, size = 0, slot = 0, name = ExtractSearchRewriteQuery, category = ad, field = 203, feature_size = 5000001
remap_slot = 165, dim = 16, map_slot = 165, size = 0, slot = 0, name = ExtractSearchQrScore, category = ad, field = 204, feature_size = 1001
remap_slot = 166, dim = 16, map_slot = 166, size = 0, slot = 0, name = ExtractSearchExtendType, category = ad, field = 205, feature_size = 51
remap_slot = 257, dim = 16, map_slot = 257, size = 0, slot = 0, name = ExtractUserAdFieldMmuE2ESameClusterDiffNamePhoto, category = ad, field = 206, feature_size = 100001
remap_slot = 167, dim = 16, map_slot = 167, size = 0, slot = 0, name = ExtractUserAdServerNoItemClickFlag, category = ad, field = 207, feature_size = 100001
remap_slot = 168, dim = 16, map_slot = 168, size = 0, slot = 0, name = ExtractCombineUserItemImpressionIndustryAdIndustryRealtime, category = ad, field = 208, feature_size = 1000001
remap_slot = 169, dim = 16, map_slot = 169, size = 0, slot = 0, name = ExtractCombineUserRealTimeActionAndAdInfoClick, category = ad, field = 209, feature_size = 1000001
remap_slot = 170, dim = 16, map_slot = 170, size = 0, slot = 0, name = ExtractCombineFollowAuthorId, category = ad, field = 210, feature_size = 10000000
remap_slot = 171, dim = 16, map_slot = 171, size = 0, slot = 0, name = ExtractCombinePoiAuthorId, category = ad, field = 211, feature_size = 100001
remap_slot = 172, dim = 16, map_slot = 172, size = 0, slot = 0, name = ExtractCombineUserAppListProduct, category = ad, field = 212, feature_size = 1000001
remap_slot = 173, dim = 16, map_slot = 173, size = 0, slot = 0, name = ExtractSearchKboxType, category = ad, field = 213, feature_size = 100001
remap_slot = 174, dim = 16, map_slot = 174, size = 0, slot = 0, name = ExtractCombineUserAppListAdId, category = ad, field = 214, feature_size = 100001
remap_slot = 175, dim = 16, map_slot = 175, size = 0, slot = 0, name = ExtractCombineUserAttributePhotoNew, category = ad, field = 215, feature_size = 1000001
remap_slot = 176, dim = 16, map_slot = 176, size = 0, slot = 0, name = ExtractCombineUserRealTimeActionAndAdInfoReplayed, category = ad, field = 216, feature_size = 1000001
remap_slot = 177, dim = 16, map_slot = 177, size = 0, slot = 0, name = ExtractCombineUserRealTimeActionAndAdInfoPlay3s, category = ad, field = 217, feature_size = 1000001
remap_slot = 178, dim = 16, map_slot = 178, size = 0, slot = 0, name = ExtractCombineUserLongtermActionAndAdInfoLps, category = ad, field = 218, feature_size = 1000001
remap_slot = 179, dim = 16, map_slot = 179, size = 0, slot = 0, name = ExtractCombineUserLongtermActionAndAdInfoItemclick, category = ad, field = 219, feature_size = 1000001
remap_slot = 180, dim = 16, map_slot = 180, size = 0, slot = 0, name = ExtractCombineUserLongtermActionAndAdInfoPlayend, category = ad, field = 220, feature_size = 1000001
remap_slot = 181, dim = 16, map_slot = 181, size = 0, slot = 0, name = ExtractCombineUserLongtermActionAndAdInfoPlay5s, category = ad, field = 221, feature_size = 1000001
remap_slot = 182, dim = 16, map_slot = 182, size = 0, slot = 0, name = ExtractCombineUserRealTimeActionAndAdInfoClickShort, category = ad, field = 222, feature_size = 1000001
remap_slot = 183, dim = 16, map_slot = 183, size = 0, slot = 0, name = ExtractCombineUserRealTimeActionAndAdInfoPlay5sMoreCombine, category = ad, field = 223, feature_size = 1000001
remap_slot = 184, dim = 16, map_slot = 184, size = 0, slot = 0, name = ExtractCombineUserRealTimeActionAndAdInfoPlayendMoreCombine, category = ad, field = 224, feature_size = 1000001
remap_slot = 185, dim = 16, map_slot = 185, size = 0, slot = 0, name = ExtractCombineQuerytokenAccountId, category = ad, field = 225, feature_size = 1000001
remap_slot = 186, dim = 16, map_slot = 186, size = 0, slot = 0, name = ExtractCombineQuerytokenAdCompaignId, category = ad, field = 226, feature_size = 1000001
remap_slot = 187, dim = 16, map_slot = 187, size = 0, slot = 0, name = ExtractCombineQuerytokenAdCompaignType, category = ad, field = 227, feature_size = 1000001
remap_slot = 188, dim = 16, map_slot = 188, size = 0, slot = 0, name = ExtractCombineQuerytokenAdIndustry, category = ad, field = 228, feature_size = 1000001
remap_slot = 189, dim = 16, map_slot = 189, size = 0, slot = 0, name = ExtractCombineQuerytokenAdUnitId, category = ad, field = 229, feature_size = 1000001
remap_slot = 190, dim = 16, map_slot = 190, size = 0, slot = 0, name = ExtractCombineQueryAuthor, category = ad, field = 230, feature_size = 1000001
remap_slot = 191, dim = 16, map_slot = 191, size = 0, slot = 0, name = ExtractCombineQueryDupCoverId, category = ad, field = 231, feature_size = 1000001
remap_slot = 192, dim = 16, map_slot = 192, size = 0, slot = 0, name = ExtractCombineQuerytokenPhoto, category = ad, field = 232, feature_size = 1000001
remap_slot = 193, dim = 16, map_slot = 193, size = 0, slot = 0, name = ExtractCombineQueryProductName, category = ad, field = 233, feature_size = 1000001
remap_slot = 194, dim = 16, map_slot = 194, size = 0, slot = 0, name = ExtractCombineUserProductName, category = ad, field = 234, feature_size = 1000001
remap_slot = 195, dim = 16, map_slot = 195, size = 0, slot = 0, name = ExtractSearchPhotoPname, category = ad, field = 235, feature_size = 1000001
remap_slot = 196, dim = 16, map_slot = 196, size = 0, slot = 0, name = ExtractSearchPhotoPname2, category = ad, field = 236, feature_size = 1000001
remap_slot = 197, dim = 16, map_slot = 197, size = 0, slot = 0, name = ExtractSearchPhotoAsr, category = ad, field = 237, feature_size = 1000001
remap_slot = 198, dim = 16, map_slot = 198, size = 0, slot = 0, name = ExtractSearchPhotoAsr2, category = ad, field = 238, feature_size = 1000001
remap_slot = 199, dim = 16, map_slot = 199, size = 0, slot = 0, name = ExtractSearchPhotoCname, category = ad, field = 239, feature_size = 1000001
remap_slot = 200, dim = 16, map_slot = 200, size = 0, slot = 0, name = ExtractSearchPhotoCname2, category = ad, field = 240, feature_size = 1000001
remap_slot = 201, dim = 16, map_slot = 201, size = 0, slot = 0, name = ExtractSearchPhotoDescription, category = ad, field = 241, feature_size = 1000001
remap_slot = 202, dim = 16, map_slot = 202, size = 0, slot = 0, name = ExtractSearchPhotoDescription2, category = ad, field = 242, feature_size = 1000001
remap_slot = 203, dim = 16, map_slot = 203, size = 0, slot = 0, name = ExtractSearchPhotoOcr, category = ad, field = 243, feature_size = 1000001
remap_slot = 204, dim = 16, map_slot = 204, size = 0, slot = 0, name = ExtractSearchPhotoOcr2, category = ad, field = 244, feature_size = 1000001
remap_slot = 205, dim = 16, map_slot = 205, size = 0, slot = 0, name = ExtractSearchPhotoOcrTitle, category = ad, field = 245, feature_size = 1000001
remap_slot = 206, dim = 16, map_slot = 206, size = 0, slot = 0, name = ExtractSearchPhotoOcrTitle2, category = ad, field = 246, feature_size = 1000001
remap_slot = 207, dim = 16, map_slot = 207, size = 0, slot = 0, name = ExtractSearchPhotoSlogan, category = ad, field = 247, feature_size = 1000001
remap_slot = 208, dim = 16, map_slot = 208, size = 0, slot = 0, name = ExtractSearchPhotoSlogan2, category = ad, field = 248, feature_size = 1000001
remap_slot = 209, dim = 16, map_slot = 209, size = 0, slot = 0, name = ExtractCombineUserLocAdvertiser, category = ad, field = 249, feature_size = 1000001
remap_slot = 210, dim = 16, map_slot = 210, size = 0, slot = 0, name = ExtractCombineQueryCorporation, category = ad, field = 250, feature_size = 1000001
remap_slot = 219, dim = 16, map_slot = 219, size = 0, slot = 0, name = ExtractSearchBidword, category = ad, field = 251, feature_size = 1000001
remap_slot = 220, dim = 16, map_slot = 220, size = 0, slot = 0, name = ExtractCombineQueryProductNameToken, category = ad, field = 252, feature_size = 5000001
remap_slot = 221, dim = 16, map_slot = 221, size = 0, slot = 0, name = ExtractCombineQueryProductNameTokenReverse, category = ad, field = 253, feature_size = 5000001
remap_slot = 222, dim = 16, map_slot = 222, size = 0, slot = 0, name = ExtractSearchParserTextTokenV1, category = ad, field = 254, feature_size = 101
remap_slot = 223, dim = 16, map_slot = 223, size = 0, slot = 0, name = ExtractSearchParserTextV1, category = ad, field = 255, feature_size = 5000001
remap_slot = 224, dim = 16, map_slot = 224, size = 0, slot = 0, name = ExtractSearchQueryCombineMatchNum, category = ad, field = 256, feature_size = 101
remap_slot = 258, dim = 16, map_slot = 258, size = 0, slot = 0, name = ExtractCombineUserIdAuthorAttrNew, category = ad, field = 257, feature_size = 100001
remap_slot = 259, dim = 16, map_slot = 259, size = 0, slot = 0, name = ExtractCombineUserClickAdMatchNumLong, category = ad, field = 258, feature_size = 1000001
