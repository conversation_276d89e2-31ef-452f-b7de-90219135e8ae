import kai.tensorflow as kai
MODEL_TRANS_ORIGIN='python'
from kai.tensorflow.config.ad_config.ktrain.klearn_utils import data_ops
from kai.tensorflow.config.ad_config.ktrain.AUC import auc as auc_eval
import logging
LOG_FORMAT = "%(asctime)s - %(levelname)s [%(filename)s:%(lineno)s - %(funcName)s] - %(message)s"
logging.basicConfig(level=logging.INFO, format=LOG_FORMAT)
logger = logging.getLogger(__name__)
import sys,os
import time
import math
from datetime import datetime
from datetime import timedelta
import tensorflow as tf
#import tensorflow.compat.v1 as tf
import numpy as np

def dense_load_handle(warmup_weight: dict, warmup_extra: dict, ps_weight: dict, ps_extra: dict, tf_weight: dict, load_option): 
    ''' 
    https://docs.corp.kuaishou.com/k/home/<USER>/fcAAXcP_sb-h0_8v1lEr7wIqa#section=h.jitvgok6c7vl
    - 参数一：warmup_weight，从base的model加载得到的weight，key为参数名，value为numpy形式的参数值 。 
    - 参数二：warmup_extra，从base的model加载得到的extra(optimizer 依赖参数)，key为参数名，value为numpy形式的参数值
    - 参数三：ps_weight，从参数服务器上拉取的weight，key为参数名，value为numpy形式的参数值
    - 参数四：ps_extra，从参数服务器上拉取的extra，key为参数名，value为numpy形式的参数值
    - 参数五：tf_weight，tensorflow本地通过初始化op生成的weight，key为参数名，value为numpy形式的参数值
    - 参数六：load_option，kai.load()的配置，包含加载参数的地址，加载模式等信息
    - 返回值一：weight(dict), 最终确定的weight组合，key为参数名，value为numpy形式的参数值
    - 返回值二：extra(dict), 最终确定的extra组合，key为参数名，value为numpy形式的参数值
    - 都是这种格式： {weight_name1 : np_array, weight_name2 : np_array, ... }
    '''

    import numpy as np

    weight = None
    extra = None
    dense_variable_nums = len(tf_weight)

    if warmup_weight is not None and len(warmup_weight) > 0:
        for var_name in list(warmup_weight):
            if var_name not in tf_weight: # 表示参数存在base模型，但新模型没有。即【删除参数】
                print("加载的 dense variable({}) 在运行时不存在，其值被忽略。".format(var_name))
                del warmup_weight[var_name]
                del warmup_extra[var_name]        

            elif warmup_weight[var_name].size != tf_weight[var_name].size: # base模型的参数维度和新模型不一样，即此参数被修改，需要额外处理。即【修改参数】
                if False:
                    print("加载的 dense variable({}) size ({} vs {}) 不匹配，其值被忽略".format(var_name, warmup_weight[var_name].shape, tf_weight[var_name].shape))
                    del warmup_weight[var_name]
                    del warmup_extra[var_name]
                else:
                    print("加载的 dense variable({}) size ({} vs {}) 不匹配: --> Padding".format(var_name, warmup_weight[var_name].shape, tf_weight[var_name].shape))
                    # 新增reco_user 1个, user 5个, photo 1个, combine 2个, 共计9个)  (25125 -> 25269)
                    # base [corss_embedding, combine_embedding, dense_embedding] = [23876, 800, 449]  -->
                    # test [corss_embedding, user_new_embedding, photo_new_embedding, combine_embedding, dense_embedding] = [23876, 96, 16, 800+32, 449]
                    if var_name in ('LayerNorm/beta:0', 'LayerNorm/gamma:0'):
                        warmup_weight[var_name] = np.concatenate([warmup_weight[var_name][:3584],
                                                                 tf_weight[var_name][3584:3584+144],
                                                                 warmup_weight[var_name][3584:]
                        ], axis=0)

                        assert warmup_weight[var_name].size == tf_weight[var_name].size, "dense variable({}) size ({} vs {}) 不匹配".format(var_name, warmup_weight[var_name].shape, tf_weight[var_name].shape)

                    elif var_name == 'share_bottom_layer_0/w:0':
                        warmup_weight[var_name] = np.concatenate([warmup_weight[var_name][:23876, :], # base corss wise
                                                                  tf_weight[var_name][23876:23876+96+16, :],
                                                                  warmup_weight[var_name][23876:23876+800, :],  # base combine wise
                                                                  tf_weight[var_name][23876+96+16+800:23876+96+16+832, :],  # base dense wise   
                                                                  warmup_weight[var_name][23876+800:, :]   # init dense  wise
                        ], axis=0)

                        assert warmup_weight[var_name].size == tf_weight[var_name].size, "dense variable({}) size ({} vs {}) 不匹配".format(var_name, warmup_weight[var_name].shape, tf_weight[var_name].shape)
                    else: 
                        print("加载的 dense variable({}) size ({} vs {}) 不匹配: --> 其值被忽略".format(var_name, warmup_weight[var_name].shape, tf_weight[var_name].shape))
                        del warmup_weight[var_name]
                        del warmup_extra[var_name]


        weight = warmup_weight
    else:
        weight = tf_weight # 冷启动。用tf初始化。若用 weight=ps_weight，表示weight用ps初始化。



    if warmup_extra is not None and len(warmup_extra) > 0: # 
        for var_name in list(warmup_extra):
            if var_name not in ps_extra:
                print("加载的 dense variable extra({}) 在运行时不存在，其值被忽略。".format(var_name))  # noqa
                del warmup_extra[var_name]
            elif warmup_extra[var_name].size != ps_extra[var_name].size:
                if False:
                    print("加载的 dense variable extra({}) size ({} vs {}) 不匹配，其值被忽略".format(var_name, warmup_extra[var_name].shape, ps_extra[var_name].shape))
                    del warmup_extra[var_name]

                else:
                    print("加载的 dense variable extra({}) size ({} vs {}) 不匹配: --> Padding".format(var_name, warmup_extra[var_name].shape, ps_extra[var_name].shape))
                    if var_name in ('LayerNorm/beta:0', 'LayerNorm/gamma:0'):
                        # extra 是展开的
                        warmup_extra[var_name] = warmup_extra[var_name].reshape((-1, ))
                        ps_extra[var_name]     = ps_extra[var_name].reshape((-1, ))

                        warmup_extra[var_name] = np.concatenate([warmup_extra[var_name][:3584],
                                                                 ps_extra[var_name][3584:3584+144],
                                                                 warmup_extra[var_name][3584:]
                        ], axis=0)

                        warmup_extra[var_name] = warmup_extra[var_name].reshape((-1, ))
                        assert warmup_extra[var_name].size == ps_extra[var_name].size, "dense variable extra({}) size ({} vs {}) 不匹配".format(var_name, warmup_extra[var_name].shape, ps_extra[var_name].shape)
                    
                    elif var_name == 'share_bottom_layer_0/w:0':
                        # extra 是展开的
                        warmup_extra[var_name] = warmup_extra[var_name].reshape((-1, 1024))
                        ps_extra[var_name]     = ps_extra[var_name].reshape((-1, 1024))

                         # 新增reco_user 1个, user 5个, photo 1个, combine 2个, 共计9个)  (25125 -> 25269)
                        # base [corss_embedding, combine_embedding, dense_embedding] = [23876, 800, 449]  -->
                        # test [corss_embedding, user_new_embedding, photo_new_embedding, combine_embedding, dense_embedding] = [23876, 240, 16, 800+64, 449]
                        warmup_extra[var_name] = np.concatenate([warmup_extra[var_name][:23876, :], # base corss wise
                                                                  ps_extra[var_name][23876:23876+96+16, :],
                                                                  warmup_extra[var_name][23876:23876+800, :],  # base combine wise
                                                                  ps_extra[var_name][23876+96+16+800:23876+96+16+832, :],  # base dense wise   
                                                                  warmup_extra[var_name][23876+800:, :]   # init dense  wise
                        ], axis=0)

                        warmup_extra[var_name] = warmup_extra[var_name].reshape((-1, ))

                        assert warmup_extra[var_name].size == ps_extra[var_name].size, "dense variable extra({}) size ({} vs {}) 不匹配".format(var_name, warmup_extra[var_name].shape, ps_extra[var_name].shape)
                    else: 
                        print("加载的 dense variable extra({}) size ({} vs {}) 不匹配: --> 其值被忽略".format(var_name, warmup_extra[var_name].shape, ps_extra[var_name].shape))
                        del warmup_extra[var_name]


        extra = warmup_extra
    else:
        extra = ps_extra

    if len(weight) < dense_variable_nums: # 不存在base模型里，但存在新模型里，即【新增参数】
        for var_name, var in tf_weight.items():
            if var_name not in weight: # tf_weight 是新模型的所有w，而weight是前面一项项放入的。这里表示新模型有而旧模型没有。
                weight[var_name] = var # 表示：将新增的var_name 的数值arr赋予给 weight。 此处可改为自定义初始化后的arr
                # 或者使用 numpy 自己控制数值如何初始化
                # weight[var_name] = np.array()
            
    if len(extra) < dense_variable_nums: # 不存在base模型里，但存在新模型里，即【新增extra参数】
        for var_name, var in ps_extra.items():
            if var_name not in extra: # ps_extra是新模型的所有extra，而extra是前面一项项放入的
                extra[var_name] = var # 表示：将新增的var_name 的数值arr赋予给 extra，此处可改为自定义初始化后的arr


    assert len(weight) == dense_variable_nums
    assert len(extra) == dense_variable_nums

    return weight, extra # 返回让框架来赋值


# 还需要把save hdfs中的done list删 或者配置 force_load_init_model=true，才会走init的逻辑
kai.set_load_dense_func(dense_load_handle)


def get_lookup_tbl(K):
    tbl = []
    for i in range(1 << (K)):
        items = []
        for j in range(K):
            if i & (1 << j) != 0:
                items.append(1)
            else:
                items.append(0)
        tbl.append(items)
    return tbl
def get_lookup_tbl_with_neg(K, neg_weight, neg_labels=[1, 8, 9]):
    tbl = []
    for i in range(1 << (K)):
        items = []
        for j in range(K):
            if i & (1 << j) != 0:
                items.append(1.)
            else:
                items.append(0.)
        items.append(1.) # as sample weight
        tbl.append(items)
    for neg in neg_labels:
        tbl[neg][-1] = neg_weight # for neg sample
    # tbl[neg_labels][-1] = neg_weight
    return tbl
def get_sub_matrix(pf):
    sub = [[0.0 for i in range(pf*4)] for j in range(pf*16)]
    for i in range(pf*4):
        sub[4 * i][i] = 1.0
        sub[(4 * i) + 1][i] = 1.0
        sub[(4 * i) + 2][i] = 1.0
        sub[(4 * i) + 3][i] = 1.0
    return sub
class HashDnnModel(object):
    def __init__(self):
        klearn_ops = []
        klearn_conf = type('', (), {})()
        klearn_conf.kafka_tag = ['search_lps_filter:item_impression']
        klearn_conf.user_name = 'dangpingbo'
        klearn_conf.extractor_type = 'KlearnCofeaSampleExtractor'
        klearn_conf.feature_text = './kai_feature.txt'
        klearn_conf.batch_size = 1024
        klearn_conf.klearn_conf = type('',(),{})()
        klearn_conf.klearn_conf.base_lr = 0.1
        klearn_conf.klearn_conf.batch_size = 1024
        klearn_conf.klearn_conf.decay = 0.0
        klearn_conf.klearn_conf.dnn_net_size = '1024,256,128,2'
        klearn_conf.klearn_conf.fields = 233
        klearn_conf.klearn_conf.real_dense_fields = 15
        klearn_conf.klearn_conf.embedding_size = 16
        klearn_conf.klearn_conf.eps = 0.0001
        klearn_conf.klearn_conf.exclude_dense_set = set()
        klearn_conf.klearn_conf.input_dense_total_size = 449
        klearn_conf.klearn_conf.input_dense_user_count = 7
        klearn_conf.klearn_conf.input_sparse_total_size = 3584
        klearn_conf.klearn_conf.input_sparse_user_count = 133
        klearn_conf.klearn_conf.item_type = 'AD_DSP'
        klearn_conf.klearn_conf.l2 = 0.0
        klearn_conf.klearn_conf.neg_weight = '1'
        klearn_conf.klearn_conf.ps_hash = 0
        klearn_conf.klearn_conf.tab = ''
        klearn_conf.klearn_conf.topic = 'ad_kafka_feature_dsp_click_lps'
        klearn_conf.klearn_conf.topic_id = 'ad_kafka_feature_dsp_click_lps'
        klearn_conf.klearn_conf.train_loss_diff = '0.5'
        klearn_conf.klearn_conf.use_bn = False
        klearn_conf.klearn_conf.version = 3
        klearn_conf.batch_size = 1024
        klearn_conf.model_name = 'dsp_lps_search_add_fea_kai2'
        from kai.tensorflow.config.ad_config.ktrain.klearn_utils.klearn_config import parse_klearn_feature
        parse_klearn_feature(klearn_conf)
        user_conf = klearn_conf
        self._config = klearn_conf
        self._klearn_ops = klearn_ops
        self._dnn_net_size = [int(x) for x in self._config.klearn_conf.dnn_net_size.split(',') if len(x) > 0]
        self._config.klearn_conf.user_feature_size = 127
        self._config.klearn_conf.photo_feature_size = 47
        self._config.klearn_conf.combine_feature_size = 54

        self._config.klearn_conf.new_user_feature_size = 5
        self._config.klearn_conf.new_photo_feature_size = 1
        
        self._config.klearn_conf.share_num = 1
        self._config.klearn_conf.neg_weight = float(self._config.klearn_conf.neg_weight)
        self._last_result = {}
        self.set_t = True
        if self.set_t:
            self.use_bn = False
            self.ln  = True
            self.trainable_task_weight = True
        else:
            self.use_bn = True
            self.ln = False
            self.trainable_task_weight = False
        self.weight_ctr = 0.2  # ctr
        self.weight_ctcvr = 1.0  # ctcvr
        self.lookup_tbl = get_lookup_tbl_with_neg(9, self._config.klearn_conf.neg_weight)
        self.hook_inputs = {}
        self.local_step = 0
        self.real_mean_acc = 0.0
        self.prob_mean_acc = 0.0
    def GetWriteTensor(self):
        return self.hook_inputs
    def write_hook(self, res):
        self.real_mean_acc += res['ctcvr_real_mean']
        self.prob_mean_acc += res['ctcvr_prob_mean']
        if self.local_step % 2000 == 0:
            print('================= debug_info local_step {} =================\n'.format(self.local_step))
            keys = list(self.GetWriteTensor().keys())
            if 'loss' in res:
                keys.append('loss')
            for key in keys:
                if key not in res:
                    continue
                print(key + ':', res[key])
            print('prob_mean_acc' + ':', self.prob_mean_acc / self.local_step)
            print('real_mean_acc' + ':', self.real_mean_acc / self.local_step)
        self.local_step += 1

    def get_learning_rate(self, total_train_step, cur_step, base_lr=0.007):
        if cur_step < total_train_step:
            lr = base_lr * (1.1 - math.exp(-cur_step * 2.33 / total_train_step))
            if lr > base_lr:
                lr = base_lr
            return lr
        return base_lr
    def fc(self, input, input_size, layer_size, i, is_train, is_cpu_ps):
        weight_name = 'w'
        w = data_ops.variable_on_cpu(weight_name, [input_size, layer_size],
                                     initializer=tf.random_normal_initializer(
                                         stddev=1.0 / math.sqrt(float(input_size))),
                                     is_cpu_ps=is_cpu_ps)
        bias_name = 'b'
        b = data_ops.variable_on_cpu(bias_name, [layer_size],
                                     initializer=tf.zeros_initializer,
                                     is_cpu_ps=is_cpu_ps)
        logger.info("%s length=%d * %d" % (w.name, input_size, layer_size))
        logger.info("%s length=%d" % (b.name, layer_size))
        o1 = tf.add(tf.matmul(input, w), b)
        if i != len(self._dnn_net_size) - 1:
            if self.use_bn:
                # o1 = BatchNorm(# o1, self.is_train_pl, 'bn')
                logger.info('bn in the {} layer'.format('share_bottom_layer_' + str(i)))
                o1 = kai.batch_norm(o1, 'bn')
            if self.ln:
                logger.info('ln in the {} layer'.format('share_bottom_layer_' + str(i)))
                o1 = tf.contrib.layers.layer_norm(o1)
                # o1 = vo_ln(o1)
            o = tf.nn.relu(o1)
        else:
            o = o1
        return o
    def GetMetric(self):
        return self.eval_targets
    def GetPlaceHolder(self):
        return {"is_train": self.is_train_pl}
    def GetAUC(self):
        return {"auc": self.auc}
    def kai_v2_model_def(self):
        from kai.tensorflow.nn import ParamAttr

        is_cpu_ps = False 
        is_train = True 
        hooks = []
        exclude_dense = []
        default_param_attr = ParamAttr(initializer=kai.nn.UniformInitializer(0.01),
                                      access_method=kai.nn.ProbabilityAccess(100),
                                      recycle_method=kai.nn.UnseendaysRecycle(3650000, 0.0, False))
        kai.nn.set_default_param_attr(default_param_attr)
        
        sparse = kai.new_embedding('sparse', dim=16, slots=list(range(1, 211)), expand=None)
        add_sea = kai.new_embedding('add_sea', dim=16, slots=list(range(211, 225)), expand=None)
        # 本次迭代新加特征, 对齐信息流模型(共计22个特征: 其中reco_user 1个, user 5个, photo 1个, combine 2个, 共计9个)
        add_sea_v2 = kai.new_embedding('add_sea_v2', dim=16, slots=list(range(225, 234)), expand=None)

        ExtractDenseHour = kai.get_dense_fea('ExtractDenseHour', dim=64)
        ExtractUserAdLpsNumExtendEcomDense = kai.get_dense_fea('ExtractUserAdLpsNumExtendEcomDense', dim=2)
        ExtractUserRecallTargetInfo = kai.get_dense_fea('ExtractUserRecallTargetInfo', dim=16)
        ExtractUserAdItemClickNumExtendEcomDense = kai.get_dense_fea('ExtractUserAdItemClickNumExtendEcomDense', dim=2)
        ExtractUserTextFeatureBertConv = kai.get_dense_fea('ExtractUserTextFeatureBertConv', dim=32)
        ExtractUserEcomTextFeatureCtrConv = kai.get_dense_fea('ExtractUserEcomTextFeatureCtrConv', dim=32)
        ExtractRecoSlideFmRe = kai.get_dense_fea('ExtractRecoSlideFmRe', dim=128)
        ExtractPhotoAutoCpaBidNebula = kai.get_dense_fea('ExtractPhotoAutoCpaBidNebula', dim=1)
        ExtractPhotoCpaBid = kai.get_dense_fea('ExtractPhotoCpaBid', dim=1)
        ExtractPhotoEmbeddingFeature2 = kai.get_dense_fea('ExtractPhotoEmbeddingFeature2', dim=64)
        ExtractPhotoVisionFeatureCoverCTR = kai.get_dense_fea('ExtractPhotoVisionFeatureCoverCTR', dim=32)
        ExtractAdLpsPosterRate = kai.get_dense_fea('ExtractAdLpsPosterRate', dim=12)
        ExtractEcomProductCoverEmb = kai.get_dense_fea('ExtractEcomProductCoverEmb', dim=32)
        ExtractNumDense = kai.get_dense_fea('ExtractNumDense', dim=16)
        ExtractMatchDenseNum14Days = kai.get_dense_fea('ExtractMatchDenseNum14Days', dim=15)
        dense = tf.concat([ExtractDenseHour, ExtractUserAdLpsNumExtendEcomDense, ExtractUserRecallTargetInfo, ExtractUserAdItemClickNumExtendEcomDense, ExtractUserTextFeatureBertConv, ExtractUserEcomTextFeatureCtrConv, ExtractRecoSlideFmRe, ExtractPhotoAutoCpaBidNebula, ExtractPhotoCpaBid, ExtractPhotoEmbeddingFeature2, ExtractPhotoVisionFeatureCoverCTR, ExtractAdLpsPosterRate, ExtractEcomProductCoverEmb, ExtractNumDense, ExtractMatchDenseNum14Days], axis=1)
        dnn_input = tf.concat([sparse, add_sea, add_sea_v2, dense], axis=1)
        dnn_input_list = [sparse, add_sea, add_sea_v2, dense]
        labels = kai.get_label('label', from_sparse=True)
        labels = tf.cast(labels, tf.int32)
        #下面的block_data、 non_block_data只是为了完成兼容，用户可以自行修改
        self.block_data = [type('', (), dict(output=input))() for input in dnn_input_list]
        self.non_block_data = type('', (), dict(label_pl=labels))()
        train_loss = self.inference(dnn_input, labels, is_cpu_ps, is_train, hooks, exclude_dense)
        sparse_optimizer = kai.optimizer.AdagradW(learning_rate=0.1, eps=0.0001, decay=0.0, l2=0.0, version=3)
        dense_optimizer = kai.optimizer.AdagradW(learning_rate=0.1, eps=0.0001, decay=0.0, l2=0.0, version=2)
        # 为了兼容kai python，sparse_loss乘上batch_size * worker_num；dense_loss乘上batch_size；
        sparse_optimizer.minimize(train_loss * tf.cast(tf.shape(labels)[0], tf.float32) * kai.worker_num(), var_list=kai.get_collection(kai.GraphKeys.EMBEDDING_INPUT))
        dense_optimizer.minimize(train_loss * tf.cast(tf.shape(labels)[0], tf.float32), var_list=kai.get_collection(kai.GraphKeys.TRAINABLE_VARIABLES))
        # sparse_optimizer.minimize(train_loss, var_list=kai.get_collection(kai.GraphKeys.EMBEDDING_INPUT))
        # dense_optimizer.minimize(train_loss / kai.worker_num(), var_list=kai.get_collection(kai.GraphKeys.TRAINABLE_VARIABLES))
        return {'optimizer': [sparse_optimizer, dense_optimizer], 'metrics': self.eval_targets}

    def inference(self, dnn_input, labels, is_cpu_ps, is_train, hooks, exclude_dense):
        self.is_train_pl = kai.get_train_placeholder()
        labels = tf.reshape(tf.cast(labels, tf.int32), [-1])
        sparse_units = len(self._config.klearn_conf.fields) * self._config.klearn_conf.embedding_size
        dense_units = sum(self._config.klearn_conf.real_dense_fields)

        sparse_input = self.block_data[0].output
        
        add_sea_input = self.block_data[1].output
        logger.info("add_sea_input shape: {}".format(add_sea_input.get_shape()))

        add_sea_v2_input = self.block_data[2].output
        logger.info("add_sea_v2_input shape: {}".format(add_sea_v2_input.get_shape()))
        
        dense_input = self.block_data[3].output
        logger.info("dense_input shape: {}".format(dense_input.get_shape()))

        dnn_input = tf.concat([sparse_input, add_sea_input, add_sea_v2_input, dense_input], axis = 1)
        input_size = dnn_input.get_shape().as_list()[1]
        logger.info('sparse_units, {0}, dense_units, {1}, input_size, {2}'.format(sparse_units, dense_units, input_size))

        input = dnn_input
        logger.info(
            'sparse_units, {0}, dense_units, {1}, input_size, {2}'.format(sparse_units, dense_units, input_size))
        logger.info("dnn_input shape: {}".format(dnn_input.get_shape()))
        # labels = tf.Print(labels, [labels], message='raw labels: ', first_n=3, summarize=1000)
        labels = tf.reshape(labels, [-1])
        logger.info("origin_1 label shape: {}".format(labels.get_shape()))
        tbl = self.lookup_tbl
        if not is_train:  # sample_weight = 1 in validation
            for i in range(len(tbl)):
                tbl[i][-1] = 1.
        labels = tf.gather(tbl, labels)
        logger.info("origin_2 label shape: {}".format(labels.get_shape()))
        # labels = tf.Print(labels, [labels], message='converted labels: ', first_n=3, summarize=2000)

         # 搜索 label 处理
        label_search = labels[:, 0]
        search_cnt = tf.count_nonzero(label_search)
        MIN_CNT = 5
        label_search = tf.where(tf.greater(search_cnt, MIN_CNT), label_search, tf.ones_like(label_search))
        label_feed = tf.where(tf.greater(search_cnt, MIN_CNT), 1 - label_search, tf.ones_like(label_search))
        # label_feed = 1 - label_search
        label_search = tf.cast(label_search, tf.float32)
        label_feed = tf.cast(label_feed, tf.float32)

        label_ctr = tf.cast(labels[:, 1], tf.int32)
        label_ctcvr = tf.cast(labels[:, 2], tf.int32) # lps
        label_pxr = tf.cast(labels[:, 3], tf.int32)
        label_playend = tf.cast(labels[:, 4], tf.int32)
        label_approximate_purchase = tf.cast(labels[:, 5], tf.int32)
        label_leave_time = tf.cast(labels[:, 7], tf.int32)
        label_ad_like = tf.cast(labels[:, 8], tf.int32)

        sample_weight = labels[:, -1]
        n_samples = tf.reduce_sum(sample_weight)
        # n_samples = tf.Print(n_samples,[n_samples], message='n_samples: ', first_n=3, summarize=2000)

        n_samples_feed = tf.reduce_sum(sample_weight * label_feed)
        # n_samples_feed = tf.Print(n_samples_feed,[n_samples_feed], message='n_samples_feed: ', first_n=3, summarize=2000)

        n_samples_search = tf.reduce_sum(sample_weight * label_search)
        # n_samples_search = tf.Print(n_samples_search,[n_samples_search], message='n_samples_search: ', first_n=3, summarize=2000)

        if self.use_bn:
            logger.info('bn in the embedding layer')
            dnn_input = kai.batch_norm(dnn_input, 'bn')
        if self.ln:
            logger.info('ln in the embedding layer')
            dnn_input = tf.contrib.layers.layer_norm(dnn_input)
            # dnn_input = vo_ln(dnn_input)
        # cross
        user_units = self._config.klearn_conf.user_feature_size * self._config.klearn_conf.embedding_size
        photo_units = self._config.klearn_conf.photo_feature_size * self._config.klearn_conf.embedding_size
        combine_units = self._config.klearn_conf.combine_feature_size * self._config.klearn_conf.embedding_size
        cross_units = (self._config.klearn_conf.user_feature_size * self._config.klearn_conf.photo_feature_size) * self._config.klearn_conf.embedding_size
        #dnn_input = dnn_input/1.1
        embedding = tf.slice(dnn_input, [0, 0], [-1, sparse_units])
        dense = tf.slice(dnn_input, [0, sparse_units], [-1, dense_units])
        logger.info("sparse embedding shape: {}".format(embedding.get_shape()))
        logger.info("dense embedding shape: {}".format(dense.get_shape()))

        '''
        新增特征v2, 对齐信息流高重要性特征 add by dangpingbo
        新增部分特征为: extra_reco_user_v2, extra_user_v2, extra_photo_v2 和 extra_combine_v2
        '''
        # 01. 定义新老特征feature count
        base_user_count = 119
        base_combine_count = 44

        extra_user_count = 8
        extra_combine_count = 6

        extra_reco_user_v2_count = 1
        extra_user_v2_count = 5
        extra_photo_v2_count = 1
        extra_combine_v2_count = 2

        # 02. 计算feature count * embedding size
        base_user_units = base_user_count * self._config.klearn_conf.embedding_size
        extra_user_units = extra_user_count * self._config.klearn_conf.embedding_size

        base_combine_units = base_combine_count * self._config.klearn_conf.embedding_size
        extra_combine_units = extra_combine_count * self._config.klearn_conf.embedding_size
        
        extra_reco_user_v2_units = extra_reco_user_v2_count * self._config.klearn_conf.embedding_size
        extra_user_v2_units = extra_user_v2_count * self._config.klearn_conf.embedding_size
        extra_photo_v2_units = extra_photo_v2_count * self._config.klearn_conf.embedding_size
        extra_combine_v2_units = extra_combine_v2_count * self._config.klearn_conf.embedding_size

        logger.info("label_ctcvr shape: {}".format(label_ctcvr.get_shape()))
        logger.info(" extra reco user units {}, base user units {}, extra user units {}, extra user v2 units {}, combine units {}, extra combine units {}, extra combine v2 units {}, photo units {}, extra photo v2 unnits".format(
            extra_reco_user_v2_units, base_user_units, extra_user_units, extra_user_v2_units, base_combine_units, extra_combine_units, extra_combine_v2_units, photo_units, extra_photo_v2_units))
        
        # 03. 从原始embedding 切分 embeeding
        # 线上模型的embedding的拼接方式 base_user + base_photo + base_combine + extra_user + extra_combine + dense
        # 加入v2特征后, 按照model-feature 中 fields顺序进行更改 base_user->base_photo->base_combine->extra_user->extra_combine->reco_user_v2->extra_user_v2->extra_photo_v2->extra_combine_v2->dense
        # base 特征切分
        base_user_embedding = tf.slice(
            embedding, [0, 0], [-1, base_user_units])
        base_photo_embedding = tf.slice(
            embedding, [0, base_user_units], [-1, photo_units])
        base_combine_embedding = tf.slice(
            embedding, [0, base_user_units + photo_units], [-1, base_combine_units])
        # extra 特征切分
        extra_user_embedding = tf.slice(embedding, [
            0, base_user_units + photo_units + base_combine_units], [-1, extra_user_units])
        extra_combine_embedding = tf.slice(embedding, [
            0, base_user_units + photo_units + base_combine_units + extra_user_units], [-1, extra_combine_units])
        # extra_v2 特征切分
        extra_reco_user_v2_embedding = tf.slice(embedding, [
            0, base_user_units + photo_units + base_combine_units + extra_user_units + extra_combine_units], [-1, extra_reco_user_v2_units])
        extra_user_v2_embedding = tf.slice(embedding, [
            0, base_user_units + photo_units + base_combine_units + extra_user_units + extra_combine_units + extra_reco_user_v2_units], [-1, extra_user_v2_units])
        extra_photo_v2_embedding = tf.slice(embedding, [
            0, base_user_units + photo_units + base_combine_units + extra_user_units + extra_combine_units + extra_reco_user_v2_units + extra_user_v2_units], [-1, extra_photo_v2_units])
        extra_combine_v2_embedding = tf.slice(embedding, [
            0, base_user_units + photo_units + base_combine_units + extra_user_units + extra_combine_units + extra_reco_user_v2_units + extra_user_v2_units + extra_photo_v2_units], [-1, extra_combine_v2_units])

        logger.info("base_user_embedding shape: {}".format(base_user_embedding.get_shape()))
        logger.info("base_photo_embedding shape: {}".format(base_photo_embedding.get_shape()))
        logger.info("base_combine_embedding shape: {}".format(base_combine_embedding.get_shape()))
        logger.info("extra_user_embedding shape: {}".format(extra_user_embedding.get_shape()))
        logger.info("extra_combine_embedding shape: {}".format(extra_combine_embedding.get_shape()))
        logger.info("extra_reco_user_v2_embedding shape: {}".format(extra_reco_user_v2_embedding.get_shape()))
        logger.info("extra_user_v2_embedding shape: {}".format(extra_user_v2_embedding.get_shape()))
        logger.info("extra_photo_v2_embedding shape: {}".format(extra_photo_v2_embedding.get_shape()))
        logger.info("extra_combine_v2_embedding shape: {}".format(extra_combine_v2_embedding.get_shape()))

        embedding_user = tf.concat([base_user_embedding, extra_user_embedding], axis = 1)
        embedding_photo = tf.concat([base_photo_embedding], axis = 1)
        # 新的 user & photo embedding 单独处理
        embedding_user_new = tf.concat([extra_reco_user_v2_embedding, extra_user_v2_embedding], axis = 1)
        embedding_photo_new = tf.concat([extra_photo_v2_embedding], axis = 1)
        embedding_combine = tf.concat([base_combine_embedding, extra_combine_embedding, extra_combine_v2_embedding], axis = 1)
        logger.info("embedding_user shape: {}".format(embedding_user.get_shape()))
        logger.info("embedding_photo shape: {}".format(embedding_photo.get_shape()))
        logger.info("embedding_combine shape: {}".format(embedding_combine.get_shape()))

        # 04. user-photo cross 特征
        rep_user = tf.constant(np.tile(np.eye(self._config.klearn_conf.embedding_size), self._config.klearn_conf.photo_feature_size),
                               dtype=tf.float32)
        # user_f = 80, photo_f = 20
        embedding_user = tf.reshape(embedding_user, [-1, self._config.klearn_conf.user_feature_size, self._config.klearn_conf.embedding_size])
        embedding_user = tf.matmul(embedding_user, rep_user)  # 1024*80*16 x 16*320 = 1024*80*320
        embedding_photo = tf.reshape(embedding_photo, [-1, 1, self._config.klearn_conf.photo_feature_size * self._config.klearn_conf.embedding_size])
        embedding_cross = tf.multiply(embedding_user, embedding_photo)  # 1024*80*20 *16
        w_1 = tf.constant(get_sub_matrix(self._config.klearn_conf.photo_feature_size), dtype=tf.float32)  # 16*16
        embeddding_cross_8sum = tf.matmul(embedding_cross, w_1)  # 1024*80*320   x 320*160 = 1024*80*160
        embedding_cross = tf.reshape(embeddding_cross_8sum, [-1, cross_units // 4])

        logger.info("embedding_cross shape: {}".format(embedding_cross.get_shape()))
        dnn_input = tf.concat([embedding_cross, embedding_user_new, embedding_photo_new, embedding_combine, dense], 1)
        input_size = dnn_input.get_shape().as_list()[1]
        input = dnn_input
        # share bottom
        for i in range(self._config.klearn_conf.share_num):
            with tf.variable_scope("share_bottom_layer_{}".format(i)):
                layer_size = self._dnn_net_size[i]
                input = self.fc(input, input_size, layer_size, i, is_train, is_cpu_ps)
                input_size = layer_size

        other_input = pxr_input = ped_input = purchase_input = leave_input = input
        other_input_size = pxr_input_size = ped_input_size = purchase_input_size = leave_input_size = input_size

        # pxr_head
        for i in range(self._config.klearn_conf.share_num, len(self._dnn_net_size)):
            with tf.variable_scope("pxr_layer_{}".format(i)):
                layer_size = self._dnn_net_size[i]
                pxr_input = self.fc(pxr_input, pxr_input_size, layer_size, i, is_train, is_cpu_ps)
                pxr_input_size = layer_size
        pxr_prob = tf.nn.softmax(pxr_input, name="pxr_softmax")
        pxr_cross_entropy = tf.divide(
            tf.reduce_sum(sample_weight * tf.nn.sparse_softmax_cross_entropy_with_logits(labels=label_pxr,
                                                                                         logits=pxr_input)), n_samples,
            name='pxr_xentropy')

        # ped_head
        for i in range(self._config.klearn_conf.share_num, len(self._dnn_net_size)):
            with tf.variable_scope("ped_layer_{}".format(i)):
                layer_size = self._dnn_net_size[i]
                ped_input = self.fc(ped_input, ped_input_size, layer_size, i, is_train, is_cpu_ps)
                ped_input_size = layer_size
        ped_prob = tf.nn.softmax(ped_input, name="ped_softmax")
        ped_cross_entropy = tf.divide(
            tf.reduce_sum(sample_weight * tf.nn.sparse_softmax_cross_entropy_with_logits(labels=label_playend,
                                                                                         logits=ped_input)), n_samples,
            name='ped_xentropy')

        # ctr head
        for i in range(self._config.klearn_conf.share_num, len(self._dnn_net_size)):
            with tf.variable_scope("ctr_upper_layer_{}".format(i)):
                layer_size = self._dnn_net_size[i]
                other_input = self.fc(
                    other_input, other_input_size, layer_size, i, is_train, is_cpu_ps)
                other_input_size = layer_size
        other_prob = tf.nn.softmax(other_input, name="ctr_softmax")
        other_cross_entropy = tf.divide(
            tf.reduce_sum(sample_weight * tf.nn.sparse_softmax_cross_entropy_with_logits(labels=label_ctr,
                                                                                         logits=other_input)),
            n_samples, name='ctr_xentropy')
        
        # purchase head
        for i in range(self._config.klearn_conf.share_num, len(self._dnn_net_size)):
            with tf.variable_scope("purchase_layer_{}".format(i)):
                layer_size = self._dnn_net_size[i]
                purchase_input = self.fc(purchase_input, purchase_input_size, layer_size, i, is_train, is_cpu_ps)
                purchase_input_size = layer_size
        purchase_prob = tf.nn.softmax(purchase_input, name="purchase_softmax")
        purchase_cross_entropy = tf.divide(
            tf.reduce_sum(sample_weight * tf.nn.sparse_softmax_cross_entropy_with_logits(labels=label_approximate_purchase,
                                                                                         logits=purchase_input)),
            n_samples, name='purchase_xentropy')
  
        # leave time head
        for i in range(self._config.klearn_conf.share_num, len(self._dnn_net_size)):
            with tf.variable_scope("leavetime_layer_{}".format(i)):
                layer_size = self._dnn_net_size[i]
                leave_input = self.fc(leave_input, leave_input_size, layer_size, i, is_train, is_cpu_ps)
                leave_input_size = layer_size
        leavetime_prob = tf.nn.softmax(leave_input, name="leavetime_softmax")
        leavetime_cross_entropy = tf.divide(
            tf.reduce_sum(sample_weight * tf.nn.sparse_softmax_cross_entropy_with_logits(labels=label_leave_time,
                                                                                         logits=leave_input)),
            n_samples, name='leavetime_xentropy')

        # ctcvr head
        for i in range(self._config.klearn_conf.share_num, len(self._dnn_net_size)):
            with tf.variable_scope("upper_layer_{}".format(i)):
                layer_size = self._dnn_net_size[i]
                input = self.fc(input, input_size, layer_size, i, is_train, is_cpu_ps)
                input_size = layer_size
        prob = tf.nn.softmax(input, name="softmax")
        cross_entropy = tf.divide(
            tf.reduce_sum(sample_weight * tf.nn.sparse_softmax_cross_entropy_with_logits(labels=label_ctcvr,
            logits=input)), n_samples, name='xentropy')

        if self.trainable_task_weight:
            logger.info("use trainable_task_weight")
            w_ctcvr = data_ops.variable_on_cpu('w_ctcvr', [1],
                                               initializer=tf.zeros_initializer,
                                               is_cpu_ps=is_cpu_ps)
            w_ctr = data_ops.variable_on_cpu('w_ctr', [1],
                                             initializer=tf.zeros_initializer,
                                             is_cpu_ps=is_cpu_ps)
            w_pxr = data_ops.variable_on_cpu('w_pxr', [1],
                                             initializer=tf.zeros_initializer,
                                             is_cpu_ps=is_cpu_ps)
            w_ped = data_ops.variable_on_cpu('w_ped', [1],
                                             initializer=tf.zeros_initializer,
                                             is_cpu_ps=is_cpu_ps)
            # search purchase submit adlike leavetime
            w_purchase = data_ops.variable_on_cpu('w_purchase', [1],
                                             initializer=tf.zeros_initializer,
                                             is_cpu_ps=is_cpu_ps)
            w_leavetime = data_ops.variable_on_cpu('w_leavetime', [1],
                                             initializer=tf.zeros_initializer,
                                             is_cpu_ps=is_cpu_ps)                                                                                                   
            train_loss = tf.add(
                cross_entropy * tf.exp(-w_ctcvr) + 0.5 * w_ctcvr +
                pxr_cross_entropy *
                tf.exp(-w_pxr) + 0.5 * w_pxr + other_cross_entropy *
                tf.exp(-w_ctr) + 0.5 * w_ctr,
                ped_cross_entropy *
                tf.exp(-w_ped) + 0.5 * w_ped +
                purchase_cross_entropy *
                tf.exp(-w_purchase) + 0.5 * w_purchase + leavetime_cross_entropy *
                tf.exp(-w_leavetime) + 0.5 * w_leavetime,
                name='loss_weighted_sum')
        else:
            logger.info("use not trainable_task_weight")
            logger.info('weight_ctcvr:{}, weight_ctr:{}'.format(self.weight_ctcvr, self.weight_ctr))
            train_loss = tf.add(self.weight_ctcvr * cross_entropy, self.weight_ctr * other_cross_entropy,
                                name='loss_weighted_sum')
        self.prob = tf.clip_by_value(prob[:,1], tf.constant(0.0), tf.constant(1.0))        
        self.label = label_ctcvr

        pxr_prob = tf.clip_by_value(pxr_prob[:,1], tf.constant(0.0), tf.constant(1.0))
        ped_prob = tf.clip_by_value(ped_prob[:,1], tf.constant(0.0), tf.constant(1.0))
        other_prob = tf.clip_by_value(other_prob[:,1], tf.constant(0.0), tf.constant(1.0))

        # search purchase fill adlike leavetime
        purchase_prob = tf.clip_by_value(purchase_prob[:,1], tf.constant(0.0), tf.constant(1.0))
        leavetime_prob = tf.clip_by_value(leavetime_prob[:,1], tf.constant(0.0), tf.constant(1.0))
        
        with tf.device('/cpu:0'):
            _, self.auc, reset_auc_eval = auc_eval(self.label, self.prob)
        self.eval_targets = [
            ("CVR_AUC", self.prob, tf.cast(self.label, tf.float32), tf.cast(tf.ones_like(self.prob), tf.float32), "auc"),
            ("PXR_AUC", pxr_prob, tf.cast(label_pxr, tf.float32), tf.cast(tf.ones_like(pxr_prob), tf.float32), "auc"),
            ("PED_AUC", ped_prob, tf.cast(label_playend, tf.float32), tf.cast(tf.ones_like(ped_prob), tf.float32), "auc"),
            ("CTR_AUC", other_prob, tf.cast(label_ctr, tf.float32), tf.cast(tf.ones_like(other_prob), tf.float32), "auc"),
            ("PUR_AUC", purchase_prob, tf.cast(label_approximate_purchase, tf.float32), tf.cast(tf.ones_like(purchase_prob), tf.float32), "auc"),
            ("LET_AUC", leavetime_prob, tf.cast(label_leave_time, tf.float32), tf.cast(tf.ones_like(leavetime_prob), tf.float32), "auc")
        ] 
        
        self.hook_inputs['train_loss'] = train_loss
        self.hook_inputs['lps_cross_entropy'] = cross_entropy
        self.hook_inputs['pxr_cross_entropy'] = pxr_cross_entropy
        self.hook_inputs['ctr_cross_entropy'] = other_cross_entropy
        self.hook_inputs['ped_cross_entropy'] = ped_cross_entropy
        self.hook_inputs['purchase_cross_entropy'] = purchase_cross_entropy
        self.hook_inputs['leavetime_cross_entropy'] = leavetime_cross_entropy

        self.hook_inputs['w_ctcvr'] = w_ctcvr
        self.hook_inputs['w_ctr'] = w_ctr
        self.hook_inputs['w_pxr'] = w_pxr
        self.hook_inputs['w_ped'] = w_ped
        self.hook_inputs['w_purchase'] = w_purchase
        self.hook_inputs['w_leavetime'] = w_leavetime
        self.hook_inputs['auc'] = self.auc

        self.ctcvr_prob_mean = tf.reduce_mean(prob[:, 1], name="ctcvr_prob_mean")
        self.ctcvr_real_mean = tf.reduce_mean(tf.cast(label_ctcvr, tf.float32), name="ctcvr_real_mean")
        self.hook_inputs['ctcvr_prob_mean'] = self.ctcvr_prob_mean
        self.hook_inputs['ctcvr_real_mean'] = self.ctcvr_real_mean

        return train_loss
